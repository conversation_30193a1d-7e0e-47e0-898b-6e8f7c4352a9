import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import math
from datetime import datetime
import json
import os
import traceback
import sys

class EquipmentManager:
    """设备表管理类，用于管理项目中的设备信息"""
    
    def __init__(self, main_app):
        """
        初始化设备管理器
        
        Args:
            main_app: GasCalculator实例，用于访问主应用程序的方法和变量
        """
        self.main_app = main_app  # 保存对主应用程序的引用
        
        # 设备相关变量初始化
        self.equipment_data = []  # 用于存储设备数据的列表
        self.equipment_window = None  # 设备表窗口
        self.tree_view = None  # 设备表的树形视图组件
        
        # 引用主应用程序的属性
        self.history_file = main_app.history_file if hasattr(main_app, 'history_file') else None
        self.project_name = main_app.project_name if hasattr(main_app, 'project_name') else None
        self.project_code = main_app.project_code if hasattr(main_app, 'project_code') else None
        self.daily_capacity = main_app.daily_capacity if hasattr(main_app, 'daily_capacity') else None
        
        # 设备类型选项
        self.equipment_types = [
            "调节阀门", "安全阀", "截止阀", "球阀", "蝶阀", 
            "电磁阀", "自力式调节阀", "自力式压差阀", "自力式压力阀",
            "防爆电磁阀", "减压阀", "管道过滤器", "疏水阀", "其他"
        ]
        
        # 设备材质选项
        self.materials = [
            "铸铁", "碳钢", "不锈钢", "铜合金", "铝合金", "塑料", "其他"
        ]
        
        # 设备规格型号模板
        self.spec_templates = {
            "调节阀门": "DN{} PN{}",
            "安全阀": "A{}",
            "截止阀": "J{}",
            "球阀": "Q{}",
            "蝶阀": "D{}",
            "电磁阀": "ZQDF-{}"
            # 其他类型可以根据需要添加
        }
        
        # 子设备数据结构初始化
        self.sub_equipment_data = {}  # 用于存储子设备数据的字典，键为父设备ID
        # 绑定调节阀管径和阀芯尺寸变化事件
        if hasattr(main_app, 'main_valve_diameter_value'):
            main_app.main_valve_diameter_value.trace_add("write", self.update_valve_specification)

        if hasattr(main_app, 'main_valve_core_size_value'):
            main_app.main_valve_core_size_value.trace_add("write", self.update_valve_specification)
        # 确保绑定调节阀管径和阀芯尺寸变化事件
        if hasattr(main_app, 'main_valve_diameter_value'):
            main_app.main_valve_diameter_value.trace_add("write", self.update_valve_specification)
        
        if hasattr(main_app, 'main_valve_core_size_value'):
            main_app.main_valve_core_size_value.trace_add("write", self.update_valve_specification)
        if hasattr(main_app, 'main_valve_diameter_value'):
            main_app.main_valve_diameter_value.trace_add("write", self.update_valve_specification)
        if hasattr(main_app, 'main_valve_core_size_value'):
            main_app.main_valve_core_size_value.trace_add("write", self.update_valve_specification)
        if hasattr(main_app, 'main_valve_c_selected_value'):
            main_app.main_valve_c_selected_value.trace_add("write", self.update_valve_c_selected)
        

       
        # 监听小炉选取阀后管径变化，也需要更新安全阀参数
        if hasattr(self.main_app, 'furnace_data'):
            for i, furnace in enumerate(self.main_app.furnace_data):
                if 'selected_post_diameter' in furnace:
                    furnace['selected_post_diameter'].trace_add("write", self.update_safety_valve_parameters)
        
        # 确保正确绑定normal_flow变量的变化事件
        if hasattr(main_app, 'normal_flow'):
            # 这里修正绑定方式，使用lambda确保传递参数正确
            main_app.normal_flow.trace_add("write", lambda *args: self.update_valve_flow_rate())

        # 添加对调节阀前后压力变化的监听
        if hasattr(main_app, 'main_valve_pre'):
            main_app.main_valve_pre.trace_add("write", self.update_valve_pressure)
        
        if hasattr(main_app, 'main_valve_post'):
            main_app.main_valve_post.trace_add("write", self.update_valve_pressure)
        # 在EquipmentManager的__init__方法中添加以下代码
        # 监听小炉选取阀后管径变化
        if hasattr(self.main_app, 'furnace_data'):
            for i, furnace in enumerate(self.main_app.furnace_data):
                if 'selected_post_diameter' in furnace:
                    furnace['selected_post_diameter'].trace_add("write", self.update_gas_shutoff_valves)
        # 在__init__方法中添加监听放散阀选取DN值变化的代码
        if hasattr(self.main_app, 'release_valve_dn_value'):
            self.main_app.release_valve_dn_value.trace_add("write", self.update_safety_valve_parameters)
         # 监听总管调节阀后压力变化，更新安全阀参数
        if hasattr(self.main_app, 'main_valve_post'):
            self.main_app.main_valve_post.trace_add("write", self.update_safety_valve_parameters)


    def show_equipment_list(self, show_window=True):
        """显示设备表管理窗口"""
        # 初始化设备数据（如果不存在）
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            print("初始化设备数据...")  # 添加调试信息
            # 获取当前选取的总管阀前管径值（如果存在）
            selected_diameter = ""
            if hasattr(self.main_app, 'main_pre_diameter') and self.main_app.main_pre_diameter.get():
                selected_diameter = self.main_app.main_pre_diameter.get()
                print(f"从总管阀前选取管径获取值: {selected_diameter}")  # 添加调试信息
            # 如果没有值，就使用默认值DN200
            dn_value = "DN" + (selected_diameter if selected_diameter else "200")
            # 获取调节阀管径和阀芯尺寸
            valve_diameter = ""
            valve_core_size = ""
            if hasattr(self.main_app, 'main_valve_diameter_value') and self.main_app.main_valve_diameter_value.get():
                valve_diameter = self.main_app.main_valve_diameter_value.get()
            else:
                valve_diameter = "200"
            if hasattr(self.main_app, 'main_valve_core_size_value') and self.main_app.main_valve_core_size_value.get():
                valve_core_size = self.main_app.main_valve_core_size_value.get()
            else:
                valve_core_size = "200"
            valve_spec = f"{valve_diameter}AX{valve_core_size}A"
            c_selected = ""
            if hasattr(self.main_app, 'main_valve_c_selected_value') and self.main_app.main_valve_c_selected_value.get():
                c_selected = self.main_app.main_valve_c_selected_value.get()
            else:
                c_selected = "650"  # 默认值
            # 获取正常生产时流量 - 修复错误：获取normal_flow变量
            normal_flow = 5542  # 默认值
            if hasattr(self.main_app, 'normal_flow') and self.main_app.normal_flow.get():
                try:
                    normal_flow = float(self.main_app.normal_flow.get())
                    normal_flow = int(normal_flow)  # 转为整数
                    print(f"获取到正常生产时流量: {normal_flow}")
                except ValueError:
                    print("正常生产时流量值转换失败，使用默认值5542")
             # 获取调节阀前后压力值
            valve_pre_pressure = "0.25"  # 默认值
            valve_post_pressure = "0.1"  # 默认值
            
            if hasattr(self.main_app, 'main_valve_pre') and self.main_app.main_valve_pre.get():
                valve_pre_pressure = self.main_app.main_valve_pre.get()
                print(f"获取到调节阀前压力: {valve_pre_pressure}")
            
            if hasattr(self.main_app, 'main_valve_post') and self.main_app.main_valve_post.get():
                valve_post_pressure = self.main_app.main_valve_post.get()
                print(f"获取到调节阀后压力: {valve_post_pressure}")
            
            # 根据窑老期流量计算最大流量，向上取整
            old_flow_value = 0
            if hasattr(self.main_app, 'old_flow') and self.main_app.old_flow.get():
                try:
                    old_flow_value = float(self.main_app.old_flow.get())
                    print(f"获取到窑老期流量: {old_flow_value}")  # 添加调试信息
                except ValueError:
                    print("窑老期流量值转换失败，使用默认值0")
            
            # 最大流量约为窑老期流量的1倍，向上取整到百位
            max_flow = math.ceil(old_flow_value / 100) * 100
            if max_flow == 0:  # 如果计算得到0（可能因为没有设置窑老期流量），使用默认值6000
                max_flow = 6000
            
            flow_str = f"介质:天然气  最大流量:{max_flow}Nm³/h"
            print(f"计算得到的最大流量: {max_flow}")  # 添加调试信息
            
            self.equipment_data = [
                {
                    "序号": "1",
                    "位号": "F-01(A~B)",
                    "设备名称": "Y型天然气过滤器",
                    "型号及规格": f"{dn_value} PN16 碳钢材质 不锈钢滤网(100目)",
                    "单位": "台",
                    "数量": "2",
                    "单重": "",
                    "总重": "",
                    "设备来源": "",
                    "设备图号": "",
                    "备注": "配成套法兰及紧固、密封件(见说明)"
                },
                # 为序号1添加空行，包含流量信息
                {
                    "序号": "",
                    "位号": "",
                    "设备名称": "",
                    "型号及规格": flow_str,
                    "单位": "",
                    "数量": "",
                    "单重": "",
                    "总重": "",
                    "设备来源": "",
                    "设备图号": "",
                    "备注": ""
                },
                {
                    "序号": "2",
                    "位号": "S-01(A~B)",
                    "设备名称": "气动切断球阀",
                    "型号及规格": f"301K PN16 {dn_value} 阀体材质：碳钢 单动作",
                    "单位": "台",
                    "数量": "2",
                    "单重": "",
                    "总重": "",
                    "设备来源": "工装自控工程(无锡)有限公司",
                    "设备图号": "",
                    "备注": "天然气总管切断"
                }
            ]
            
            self.equipment_data.append({
                "序号": "",
                "位号": "",
                "设备名称": "",
                "型号及规格": "软密封 动作时间<5s 介质：天然气 故障关",
                "单位": "",
                "数量": "",
                "单重": "",
                "总重": "",
                "设备来源": "",
                "设备图号": "",
                "备注": "配成套法兰及紧固、密封件，具有火灾安全结构和防静电结构及符合相应标准的火灾安全试验证书"
            })
            # 添加电源和气源信息
            self.equipment_data.append({
                "序号": "",
                "位号": "",
                "设备名称": "二位三通电磁阀 单电控(防爆型)",
                "型号及规格": "AC220V/50Hz",
                "单位": "",
                "数量": "",
                "单重": "",
                "总重": "",
                "设备来源": "ASCO公司",
                "设备图号": "",
                "备注": ""
            })
            self.equipment_data.append({
                "序号": "",
                "位号": "",
                "设备名称": "双限位开关",
                "型号及规格": "AC220V/50Hz",
                "单位": "",
                "数量": "",
                "单重": "",
                "总重": "",
                "设备来源": "",
                "设备图号": "",
                "备注": ""
            })
            self.equipment_data.append({
                "序号": "3",
                "位号": "R-01",
                "设备名称": "套筒导向型单座调节阀",
                "型号及规格": f"501G-5200LA PN16 {valve_spec} Cv={c_selected}",
                "单位": "台",
                "数量": "1",
                "单重": "",
                "总重": "",
                "设备来源": "工装自控工程(无锡)有限公司",
                "设备图号": "",
                "备注": "天然气总管调节，带手轮"
            })
            
            self.equipment_data.append({
                "序号": "",
                "位号": "",
                "设备名称": "",
                "型号及规格": f"Qnor={normal_flow}Nm³/h 阀前压力P1={valve_pre_pressure}MPa 阀后压力P2={valve_post_pressure}MPa",
                "单位": "",
                "数量": "",
                "单重": "",
                "总重": "",
                "设备来源": "",
                "设备图号": "",
                "备注": "配成套法兰及紧固、密封件(见说明)"
            })
            self.equipment_data.append({
                "序号": "",
                "位号": "",
                "设备名称": "",
                "型号及规格": "软密封 介质：天然气 阀体材质：碳钢 等百分比特性 故障关",
                "单位": "",
                "数量": "",
                "单重": "",
                "总重": "",
                "设备来源": "",
                "设备图号": "",
                "备注": "阀门操作位置：右侧操作"
            })
            self.equipment_data.append({
                "序号": "",
                "位号": "",
                "设备名称": "附：电气阀门定位器(防爆型)",
                "型号及规格": "EPB800系列 4~20mA.DC",
                "单位": "",
                "数量": "",
                "单重": "",
                "总重": "",
                "设备来源": "",
                "设备图号": "",
                "备注": ""
            })
            
        # 对现有数据进行整合，将流量数据合并到型号及规格中
        else:
            for item in self.equipment_data:
                if "流量" in item and item["流量"]:
                    item["型号及规格"] = f"{item['型号及规格']} {item['流量']}"
                    # 删除流量字段
                    if "流量" in item:
                        del item["流量"]
        
        # 只有当show_window为True时才创建和显示窗口
        if show_window:
            # 创建设备表窗口
            equipment_window = tk.Toplevel(self.main_app.root)
            equipment_window.title("设备表")
            equipment_window.geometry("1600x800")
            # 以下两行注释掉，使窗口变为非模态
            # equipment_window.transient(self.main_app.root)
            # equipment_window.grab_set()

            # 设置窗口图标
            if hasattr(self.main_app, 'icon_path') and os.path.exists(self.main_app.icon_path):
                equipment_window.iconbitmap(self.main_app.icon_path)

            # 创建主框架
            main_frame = ttk.Frame(equipment_window)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # 创建表格区域
            table_frame = ttk.LabelFrame(main_frame, text="设备列表")
            table_frame.pack(fill="both", expand=True, padx=5, pady=5)

            # 创建表格列
            columns = ("序号", "位号", "设备名称", "型号及规格",  "单位", "数量",
                    "单重", "总重", "设备来源", "设备图号", "备注")

            # 创建表格 - 添加gridlines选项以显示网格线
            equipment_tree = ttk.Treeview(table_frame, columns=columns, show="headings", style="Gridded.Treeview")
            
            # 保存对树形视图的引用，以便其他方法访问
            self.tree_view = equipment_tree

            # 定义带网格线的样式
            style = ttk.Style()
            style.configure("Gridded.Treeview", rowheight=25)
            # 配置Treeview.Cell元素
            style.layout("Gridded.Treeview", [('Treeview.fill',{'sticky': 'nswe'}),
                                            ('Treeview.border',{'sticky': 'nswe', 'border': '1', 'children':
                                            [('Treeview.padding',{'sticky': 'nswe', 'children':
                                            [('Treeview.treearea',{'sticky': 'nswe'})]})]})
                                            ])
            # 配置网格线颜色
            style.configure("Gridded.Treeview",
                        background="white",
                        fieldbackground="white",
                        foreground="black",
                        borderwidth=1,           # 添加边框宽度
                        relief="solid")          # 设置边框样式为实线)
            # 强化单元格边框
            style.configure("Gridded.Treeview.Cell", borderwidth=1, relief="solid", padding=4)
            style.configure("Gridded.Treeview.Heading", borderwidth=1, relief="solid", padding=4, font=('Helvetica', 10, 'bold'))
            style.configure("Gridded.Treeview.Row", borderwidth=1, relief="solid")

            style.map("Gridded.Treeview",
                    background=[('selected', '#e6e6e6')],
                    foreground=[('selected', 'black')])

            # 设置列宽和对齐方式
            for col in columns:
                equipment_tree.column(col, width=100, anchor="center")  # 将所有列设为居中对齐

            # 调整特定列的宽度，保持居中对齐
            equipment_tree.column("序号", width=40, anchor="center")
            equipment_tree.column("位号", width=80, anchor="center")
            equipment_tree.column("设备名称", width=250, anchor="center")
            equipment_tree.column("型号及规格", width=400, anchor="center")
           
            equipment_tree.column("单位", width=40)
            equipment_tree.column("数量", width=40)
            equipment_tree.column("单重", width=40)
            equipment_tree.column("总重", width=40)
            equipment_tree.column("设备来源", width=250)
            equipment_tree.column("设备图号", width=100)
            equipment_tree.column("备注", width=250)
            # 设置表头 - 居中显示
            for col in columns:
                equipment_tree.heading(col, text=col, anchor="center")

            # 添加数据
            print(f"准备添加 {len(self.equipment_data)} 条数据到表格")  # 添加调试信息
            for item in self.equipment_data:
                values = [item.get(col, "") for col in columns]
                equipment_tree.insert("", "end", values=values)
            # 添加表格虚线
            equipment_tree.tag_configure('evenrow', background='#f0f0f0')
            equipment_tree.tag_configure('oddrow', background='white')

            # 重新应用行标签以显示交替行颜色
            for i, item in enumerate(equipment_tree.get_children()):
                tags = 'evenrow' if i % 2 == 0 else 'oddrow'
                equipment_tree.item(item, tags=(tags,))

            # 添加滚动条
            y_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=equipment_tree.yview)
            x_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=equipment_tree.xview)
            equipment_tree.configure(yscrollcommand=y_scrollbar.set, xscrollcommand=x_scrollbar.set)

            # 放置滚动条
            y_scrollbar.pack(side="right", fill="y")
            x_scrollbar.pack(side="bottom", fill="x")
            equipment_tree.pack(side="left", fill="both", expand=True)
            # 使用HTML表格风格的边框方案
            style = ttk.Style()
            style.configure("Gridded.Treeview",
                            rowheight=25,
                            borderwidth=1,
                            relief="solid",
                            background="white",
                            fieldbackground="white",
                            foreground="black")

            # 明确设置表头样式
            style.configure("Gridded.Treeview.Heading",
                            font=('Helvetica', 10, 'bold'),
                            borderwidth=1,
                            relief="solid",
                            background="#f0f0f0")

            # 创建操作按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill="x", padx=5, pady=10)

            style.configure("Separator.TSeparator", background="black")

            # 延迟调用函数添加分隔线，确保表格已经完全布局
            # 移除对不存在方法的调用，避免错误
            # table_frame.after(100, lambda: self.add_row_separators(equipment_tree, table_frame))

            # 创建操作按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill="x", padx=5, pady=10)

            # 添加编辑按钮
            ttk.Button(button_frame, text="添加设备",
                    command=lambda: self.add_edit_equipment(equipment_window, equipment_tree)).pack(side="left", padx=5)
            ttk.Button(button_frame, text="编辑设备",
                    command=lambda: self.edit_equipment(equipment_window, equipment_tree)).pack(side="left", padx=5)
            ttk.Button(button_frame, text="删除设备",
                    command=lambda: self.delete_equipment(equipment_tree)).pack(side="left", padx=5)
            # 新增添加子行按钮
            ttk.Button(button_frame, text="添加子行",
                    command=lambda: self.add_sub_row(equipment_window, equipment_tree)).pack(side="left", padx=5)
            # 新增删除子行按钮
            ttk.Button(button_frame, text="删除子行",
                    command=lambda: self.delete_sub_row(equipment_window, equipment_tree)).pack(side="left", padx=5)
            ttk.Button(button_frame, text="导出设备表",
                    command=lambda: self.export_equipment_list()).pack(side="left", padx=5)

            # 双击编辑项目
            equipment_tree.bind("<Double-1>", lambda event: self.edit_equipment(equipment_window, equipment_tree))
            
            # 在末尾添加窗口居中代码
            equipment_window.update_idletasks()  # 更新窗口，确保尺寸已确定

            # 获取主窗口和弹窗的尺寸
            root_width = self.main_app.root.winfo_width()
            root_height = self.main_app.root.winfo_height()
            root_x = self.main_app.root.winfo_x()
            root_y = self.main_app.root.winfo_y()

            window_width = equipment_window.winfo_width()
            window_height = equipment_window.winfo_height()

            # 计算居中位置
            x = root_x + (root_width - window_width) // 2
            y = root_y + (root_height - window_height) // 2

            # 设置窗口位置
            equipment_window.geometry(f"+{x}+{y}")

            # 保存对设备表窗口的引用
            self.equipment_window = equipment_window

            # 添加窗口关闭事件处理
            def on_equipment_window_close():
                # 清除树形视图引用
                self.tree_view = None
                # 销毁窗口
                self.equipment_window.destroy()
                # 清除窗口引用
                self.equipment_window = None

            # 设置窗口关闭事件处理器
            equipment_window.protocol("WM_DELETE_WINDOW", on_equipment_window_close)

    def add_row_separators(self, tree_view, parent_frame):
        """为表格添加行分隔线（可选方法）"""
        # 此方法用于添加表格行之间的分隔线
        # 使用Canvas或Frame在每行之间创建横线
        # 由于ttk.Treeview已经有了行样式设置，此方法可能不是必需的
        pass
    # 1. 添加删除子行的方法
    def delete_sub_row(self, parent_window, tree_view):
        """删除选中的子行"""
        selected_item = tree_view.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请先选择一个子行进行删除")
            return

        # 获取选中项的值
        selected_values = tree_view.item(selected_item[0], "values")

        # 检查是否是设备主行（通常序号不为空）
        if selected_values[0]:  # 序号不为空，可能是主设备行
            if messagebox.askyesno("确认", "选中项为设备主行，确定要删除吗？"):
                return self.delete_equipment(tree_view)

        # 如果是子行（序号为空），则直接删除
        if messagebox.askyesno("确认", "确定要删除选中的子行吗？"):
            selected_index = tree_view.index(selected_item[0])

            # 从数据中删除
            if 0 <= selected_index < len(self.equipment_data):
                del self.equipment_data[selected_index]

            # 从树视图中删除
            tree_view.delete(selected_item)

            # 重新应用行标签以显示交替行颜色
            for i, item in enumerate(tree_view.get_children()):
                tags = 'evenrow' if i % 2 == 0 else 'oddrow'
                tree_view.item(item, tags=(tags,))

    def add_sub_row(self, parent_window, tree_view):
        """在选中的设备下方添加空白子行"""
        selected_item = tree_view.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请先选择一个设备来添加子行")
            return

        # 获取选中项的索引
        selected_index = tree_view.index(selected_item[0])

        # 创建空白行数据
        columns = ("序号", "位号", "设备名称", "型号及规格", "单位", "数量",
            "单重", "总重", "设备来源", "设备图号", "备注")

        empty_row = {col: "" for col in columns}

        # 在数据中插入空白行
        self.equipment_data.insert(selected_index + 1, empty_row)

        # 更新树视图
        values = ["" for _ in columns]
        tree_view.insert("", selected_index + 1, values=values)

        # 重新应用行标签以显示交替行颜色
        for i, item in enumerate(tree_view.get_children()):
            tags = 'evenrow' if i % 2 == 0 else 'oddrow'
            tree_view.item(item, tags=(tags,))

    def add_edit_equipment(self, parent_window, tree_view, edit_mode=False):
        """添加或编辑设备信息"""
        # 创建对话框
        dialog = tk.Toplevel(parent_window)
        dialog.title("添加设备" if not edit_mode else "编辑设备")
        dialog.geometry("800x600")
        dialog.transient(parent_window)
        dialog.grab_set()

        # 设置窗口图标
        if hasattr(self, 'icon_path') and os.path.exists(self.icon_path):
            dialog.iconbitmap(self.icon_path)

        # 初始化变量
        equipment_vars = {}
        columns = ("序号", "位号", "设备名称", "型号及规格",  "单位", "数量",
               "单重", "总重", "设备来源", "设备图号", "备注")

        # 如果是编辑模式，获取选中的项目
        if edit_mode:
            selected_item = tree_view.selection()
            if not selected_item:
                messagebox.showwarning("警告", "请先选择一个设备进行编辑")
                dialog.destroy()
                return

            selected_values = tree_view.item(selected_item[0], "values")
            for i, col in enumerate(columns):
                equipment_vars[col] = tk.StringVar(value=selected_values[i] if i < len(selected_values) else "")
        else:
            # 添加模式，使用空白值
            for col in columns:
                equipment_vars[col] = tk.StringVar(value="")

            # 设置默认序号为最大序号+1
            max_seq = 0
            for item in self.equipment_data:
                try:
                    seq = int(item.get("序号", "0"))
                    if seq > max_seq:
                        max_seq = seq
                except ValueError:
                    pass
            equipment_vars["序号"].set(str(max_seq + 1))

        # 创建输入表单
        form_frame = ttk.Frame(dialog)
        form_frame.pack(fill="both", expand=True, padx=20, pady=20)
        # 使用网格布局放置表单项
        row = 0
        for col in columns:
            ttk.Label(form_frame, text=f"{col}:").grid(row=row, column=0, sticky="w", padx=5, pady=5)

            # 根据字段类型创建不同的输入控件
            if col in ["设备名称", "型号及规格",  "备注"]:
                # 使用文本框创建多行输入
                text_widget = tk.Text(form_frame, height=3, width=40)
                text_widget.grid(row=row, column=1, sticky="ew", padx=5, pady=5)
                text_widget.insert("1.0", equipment_vars[col].get())

                # 使用自定义绑定来更新StringVar
                def on_text_change(event, var=equipment_vars[col], widget=text_widget):
                    var.set(widget.get("1.0", "end-1c"))

                text_widget.bind("<KeyRelease>", on_text_change)
            else:
                # 使用常规输入框
                ttk.Entry(form_frame, textvariable=equipment_vars[col], width=40).grid(
                    row=row, column=1, sticky="ew", padx=5, pady=5)

            row += 1

        # 添加保存和取消按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def save_equipment():
            # 收集数据
            equipment_item = {}
            for col in columns:
                equipment_item[col] = equipment_vars[col].get()

            if edit_mode:
                # 更新现有项目
                selected_item = tree_view.selection()[0]
                selected_index = tree_view.index(selected_item)
                self.equipment_data[selected_index] = equipment_item

                # 更新树视图
                tree_view.item(selected_item, values=[equipment_item.get(col, "") for col in columns])
            else:
                # 添加新项目
                self.equipment_data.append(equipment_item)

                # 更新树视图
                tree_view.insert("", "end", values=[equipment_item.get(col, "") for col in columns])

            # 保存到项目数据
            self.save_equipment_data()
            dialog.destroy()

        def cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_equipment).pack(side="right", padx=5)
        ttk.Button(button_frame, text="取消", command=cancel).pack(side="right", padx=5)
        # 添加窗口居中代码
        dialog.update_idletasks()  # 更新窗口，确保尺寸已确定

        # 获取父窗口的尺寸和位置
        parent_width = parent_window.winfo_width()
        parent_height = parent_window.winfo_height()
        parent_x = parent_window.winfo_x()
        parent_y = parent_window.winfo_y()

        # 获取对话框的尺寸
        dialog_width = dialog.winfo_width()
        dialog_height = dialog.winfo_height()

        # 计算居中位置
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        # 设置窗口位置
        dialog.geometry(f"+{x}+{y}")
    # 4. 编辑已存在的设备
    def edit_equipment(self, parent_window, tree_view):
        """编辑已存在的设备信息"""
        self.add_edit_equipment(parent_window, tree_view, edit_mode=True)

    # 5. 删除设备
    def delete_equipment(self, tree_view):
        """删除选中的设备"""
        selected_item = tree_view.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请先选择一个设备进行删除")
            return

        if messagebox.askyesno("确认", "确定要删除选中的设备吗？"):
            selected_index = tree_view.index(selected_item[0])

            # 从数据中删除
            if 0 <= selected_index < len(self.equipment_data):
                del self.equipment_data[selected_index]

            # 从树视图中删除
            tree_view.delete(selected_item)

            # 保存到项目数据
            self.save_equipment_data()
    # 6. 保存设备数据到项目
    def save_equipment_data(self):
        """保存设备数据到当前项目"""
        # 获取最新的主应用程序变量引用
        self.project_name = self.main_app.project_name if hasattr(self.main_app, 'project_name') else None
        self.project_code = self.main_app.project_code if hasattr(self.main_app, 'project_code') else None
        self.daily_capacity = self.main_app.daily_capacity if hasattr(self.main_app, 'daily_capacity') else None
        self.history_file = self.main_app.history_file if hasattr(self.main_app, 'history_file') else None

        # 检查必要的属性是否存在
        if not all([self.project_name, self.project_code, self.history_file]):
            messagebox.showwarning("警告", "保存失败：缺少项目信息或历史文件路径")
            return

        # 获取当前项目数据
        project_data = {
            "工程名称": self.project_name.get(),
            "工程代号": self.project_code.get(),
            "吨位": self.daily_capacity.get() if self.daily_capacity else "",
            "设备数据": self.equipment_data
        }

        # 更新到历史记录
        try:
            history = []
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)

            # 查找是否已有当前项目
            updated = False
            for i, record in enumerate(history):
                if record.get("工程名称") == self.project_name.get() and \
                record.get("工程代号") == self.project_code.get():
                    history[i]["设备数据"] = self.equipment_data
                    updated = True
                    break

            # 如果没有找到，创建新记录
            if not updated:
                project_data["时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                history.append(project_data)

            # 保存更新后的历史记录
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("成功", "设备数据已保存")
        except Exception as e:
            error_msg = f"保存设备数据时出错: {str(e)}"
            print(error_msg)  # 在控制台打印错误信息，方便调试
            messagebox.showerror("错误", error_msg)
    # 7. 导出设备表
    def export_equipment_list(self):
        """导出设备表为Excel文件"""
        if not self.equipment_data:
            messagebox.showwarning("警告", "没有设备数据可供导出")
            return

        try:
            # 导出为Excel文件
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
                title="导出设备表"
            )

            if not file_path:
                return

            # 创建Excel工作簿和工作表
            import openpyxl
            from openpyxl.styles import Alignment, Border, Side, Font

            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "设备表"

            # 添加表头
            columns = ["序号", "位号", "设备名称", "型号及规格", "单位", "数量",
                    "单重", "总重", "设备来源", "设备图号", "备注"]

            for col_idx, col_name in enumerate(columns, 1):
                cell = ws.cell(row=1, column=col_idx, value=col_name)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center', vertical='center')
            # 添加数据
            for row_idx, item in enumerate(self.equipment_data, 2):
                for col_idx, col_name in enumerate(columns, 1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=item.get(col_name, ""))
                    cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

            # 调整列宽
            for col_idx in range(1, len(columns) + 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = 15

            # 设备名称和型号列宽度调整
            ws.column_dimensions['C'].width = 25
            ws.column_dimensions['D'].width = 40

            # 添加边框
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            for row in ws.iter_rows(min_row=1, max_row=len(self.equipment_data) + 1,
                                min_col=1, max_col=len(columns)):
                for cell in row:
                    cell.border = thin_border

            # 保存文件
            wb.save(file_path)
            messagebox.showinfo("成功", f"设备表已导出至 {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"导出设备表时出错: {str(e)}")
    # 1. 添加一个新的方法：update_equipment_diameter
    def update_equipment_diameter(self, *args):
        """当总管阀前选取管径值或窑老期流量变化时更新设备表中的管径和流量"""
        # 检查是否已经有设备数据
        if not hasattr(self, 'equipment_data'):
            return
        
        # 获取当前选取的总管阀前管径值
        selected_diameter = ""
        if hasattr(self.main_app, 'main_pre_diameter'):
            selected_diameter = self.main_app.main_pre_diameter.get()
        elif hasattr(self.main_app, 'bypass_main_pre_diameter') and self.main_app.bypass_main_pre_diameter.get():
            selected_diameter = self.main_app.bypass_main_pre_diameter.get()

        # 如果没有选择值，则不更新
        if not selected_diameter:
            return

        # 构建新的DN值字符串
        dn_value = "DN" + selected_diameter
        print(f"更新设备表管径: {dn_value}")  
        
        # 计算最大流量
        old_flow_value = 0
        if hasattr(self.main_app, 'old_flow') and self.main_app.old_flow.get():
            try:
                old_flow_value = float(self.main_app.old_flow.get())
                print(f"获取到窑老期流量: {old_flow_value}")
            except ValueError:
                pass
        
        # 最大流量约为窑老期流量的1倍，向上取整到百位
        max_flow = math.ceil(old_flow_value / 100) * 100
        if max_flow == 0:  # 如果计算得到0，使用默认值6000
            max_flow = 6000
        
        flow_str = f"介质:天然气  最大流量:{max_flow}Nm³/h"
        
        import re
        # 遍历设备数据，更新设备表1和设备表2的相关数据
        for i, item in enumerate(self.equipment_data):
            # 更新设备1(Y型天然气过滤器)的DN值
            if item.get("序号") == "1" and "过滤器" in item.get("设备名称", ""):
                current_spec = item.get("型号及规格", "")
                if "DN" in current_spec:
                    # 从字符串开头匹配DN值
                    new_spec = re.sub(r'^DN\d+', dn_value, current_spec)
                    item["型号及规格"] = new_spec
                    print(f"设备1规格从 {current_spec} 更新为 {new_spec}")
            
            # 更新设备2(气动切断阀)的DN值 - 使用简化的方式
            if item.get("序号") == "2" and "切断" in item.get("设备名称", ""):
                current_spec = item.get("型号及规格", "")
                if "DN" in current_spec:
                    # 使用相同的方式匹配和替换DN值
                    new_spec = re.sub(r'DN\s*\d+', dn_value, current_spec)
                    item["型号及规格"] = new_spec
                    print(f"设备2规格从 {current_spec} 更新为 {new_spec}")
            
            # 更新流量信息行（通常是设备1的下一行）
            if item.get("序号") == "" and "最大流量" in item.get("型号及规格", ""):
                item["型号及规格"] = flow_str
        
        # 如果设备表窗口已打开，更新显示
        if hasattr(self, 'tree_view') and self.tree_view and hasattr(self.tree_view, 'winfo_exists') and self.tree_view.winfo_exists():
            # 保存当前选择项
            selected_items = self.tree_view.selection()
            
            # 清空当前数据
            for item in self.tree_view.get_children():
                self.tree_view.delete(item)
            
            # 重新加载数据
            columns = ("序号", "位号", "设备名称", "型号及规格", "单位", "数量",
                    "单重", "总重", "设备来源", "设备图号", "备注")
            for item in self.equipment_data:
                values = [item.get(col, "") for col in columns]
                self.tree_view.insert("", "end", values=values)
            
            # 恢复选择
            for item in selected_items:
                if item in self.tree_view.get_children():
                    self.tree_view.selection_add(item)

    def refresh_equipment_table(self):
        """刷新设备表显示"""
        # 检查设备表窗口是否存在
        if hasattr(self, 'tree_view') and self.tree_view:
            # 保存当前选择项
            selected_items = self.tree_view.selection() if hasattr(self.tree_view, 'selection') else []
            
            # 清空当前数据
            try:
                for item in self.tree_view.get_children():
                    self.tree_view.delete(item)
                
                # 重新加载数据
                columns = ("序号", "位号", "设备名称", "型号及规格", "单位", "数量",
                        "单重", "总重", "设备来源", "设备图号", "备注")
                for item in self.equipment_data:
                    values = [item.get(col, "") for col in columns]
                    self.tree_view.insert("", "end", values=values)
                
                # 恢复选择
                if selected_items:
                    for item in selected_items:
                        if item in self.tree_view.get_children():
                            self.tree_view.selection_add(item)
                        
            except Exception as e:
                print(f"刷新设备表时出错: {str(e)}")

    def update_valve_specification(self, *args):
        """当主管调节阀的阀门选取管径或阀芯尺寸变化时更新设备表中的数值"""
        # 添加调试信息
        print("update_valve_specification 被调用")
        # 检查是否已经有设备数据
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            return
        
        # 获取当前调节阀管径和阀芯尺寸的值
        valve_diameter = ""
        valve_core_size = ""
        
        if hasattr(self.main_app, 'main_valve_diameter_value'):
            valve_diameter = self.main_app.main_valve_diameter_value.get()
            print(f"获取到阀门管径: {valve_diameter}")
        
        if hasattr(self.main_app, 'main_valve_core_size_value'):
            valve_core_size = self.main_app.main_valve_core_size_value.get()
            print(f"获取到阀芯尺寸: {valve_core_size}")
        
        # 如果任一值为空，使用当前设备表中的值补充
        if not valve_diameter or not valve_core_size:
            # 从设备表中查找当前规格
            current_spec = ""
            for item in self.equipment_data:
                if item.get("序号") == "3" and "调节阀" in item.get("设备名称", ""):
                    current_spec = item.get("型号及规格", "")
                    break
                
            # 使用正则表达式提取现有值
            import re
            if current_spec:
                match = re.search(r'(\d+)AX(\d+)A', current_spec)
                if match:
                    if not valve_diameter:
                        valve_diameter = match.group(1)
                        print(f"从设备表补充阀门管径: {valve_diameter}")
                    if not valve_core_size:
                        valve_core_size = match.group(2)
                        print(f"从设备表补充阀芯尺寸: {valve_core_size}")
        
        # 如果仍有任一值为空，使用默认值
        if not valve_diameter:
            valve_diameter = "200"  # 默认值
            print(f"使用默认阀门管径: {valve_diameter}")
        if not valve_core_size:
            valve_core_size = "200"  # 默认值
            print(f"使用默认阀芯尺寸: {valve_core_size}")
        
        # 构建新的规格字符串
        new_spec = f"{valve_diameter}AX{valve_core_size}A"
        print(f"生成新规格: {new_spec}")
        
        # 更新设备3的型号及规格
        updated = False
        for item in self.equipment_data:
            if item.get("序号") == "3" and "调节阀" in item.get("设备名称", ""):
                current_spec = item.get("型号及规格", "")
                print(f"当前规格: {current_spec}")
                
                # 更新规格中的阀门规格部分
                import re
                if "PN16" in current_spec:
                    # 使用正则表达式更精确地匹配和替换
                    new_spec_str = re.sub(r'(\d+)AX(\d+)A', new_spec, current_spec)
                    print(f"更新为: {new_spec_str}")
                    item["型号及规格"] = new_spec_str
                    updated = True
                    break
        
        if not updated:
            print("未找到匹配的调节阀项目或更新失败")
            
        # 强制刷新表格显示
        self.refresh_equipment_table()
        
        # 确保每次更新后保存项目
        if hasattr(self.main_app, 'save_project_silent'):
            self.main_app.save_project_silent()
        
        print("表格刷新和项目保存完成")
    def update_valve_c_selected(self, *args):
        """当C选定变化时，实时更新设备3的Cv数值"""
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            return

        c_selected = ""
        if hasattr(self.main_app, 'main_valve_c_selected_value'):
            c_selected = self.main_app.main_valve_c_selected_value.get()
        else:
            c_selected = "650"

        import re
        for item in self.equipment_data:
            if item.get("序号") == "3" and "调节阀" in item.get("设备名称", ""):
                current_spec = item.get("型号及规格", "")
                # 用正则替换Cv=xxx部分
                if "Cv=" in current_spec:
                    new_spec = re.sub(r'Cv=\d+', f'Cv={c_selected}', current_spec)
                    item["型号及规格"] = new_spec
                else:
                    # 没有Cv=，则添加
                    item["型号及规格"] = current_spec.strip() + f" Cv={c_selected}"

        self.refresh_equipment_table()

    def update_valve_flow_rate(self, *args):
        """当正常生产时流量变化时，更新设备3的流量信息行"""
        print("update_valve_flow_rate被调用") # 调试信息
        
        # 检查是否已有设备数据
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            print("设备数据不存在")
            return
        
        # 获取正常生产时流量
        normal_flow = 0
        if hasattr(self.main_app, 'normal_flow') and self.main_app.normal_flow.get():
            try:
                normal_flow = float(self.main_app.normal_flow.get())
                normal_flow = int(normal_flow)  # 转为整数
                print(f"获取到正常生产时流量: {normal_flow}")
            except ValueError:
                print("正常生产时流量值转换失败")
                return
            except Exception as e:
                print(f"处理流量时出错: {str(e)}")
                return
        
        # 找到设备3的流量信息行进行更新
        updated = False
        try:
            for i, item in enumerate(self.equipment_data):
                if item.get("序号") == "3" and "调节阀" in item.get("设备名称", ""):
                    # 找到设备3后，检查下一行是否包含流量信息
                    if i + 1 < len(self.equipment_data):
                        flow_info_row = self.equipment_data[i + 1]
                        if "Qnor=" in flow_info_row.get("型号及规格", ""):
                            old_value = flow_info_row.get("型号及规格", "")
                            flow_info_row["型号及规格"] = f"Qnor={normal_flow}Nm³/h 阀前压力P1=0.25MPa 阀后压力P2=0.1MPa"
                            print(f"设备3流量信息从 {old_value} 更新为 {flow_info_row['型号及规格']}")
                            updated = True
                            break
        except Exception as e:
            print(f"查找设备3流量行时出错: {str(e)}")
        
        if not updated:
            print("未找到设备3的流量信息行")
            return
        
        # 刷新设备表显示
        try:
            self.refresh_equipment_table()
            print("设备表已刷新")
        except Exception as e:
            print(f"刷新设备表时出错: {str(e)}")

    # 添加新方法处理阀门前后压力更新
    def update_valve_pressure(self, *args):
        """当调节阀前后压力变化时，更新设备表中的阀门压力信息"""
        print("update_valve_pressure 被调用")
        
        # 检查是否已经有设备数据
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            print("设备数据不存在")
            return
        
        # 获取主界面调节阀前后压力值
        valve_pre_pressure = ""
        valve_post_pressure = ""
        
        if hasattr(self.main_app, 'main_valve_pre') and self.main_app.main_valve_pre.get():
            valve_pre_pressure = self.main_app.main_valve_pre.get()
            print(f"获取到调节阀前压力: {valve_pre_pressure}")
        
        if hasattr(self.main_app, 'main_valve_post') and self.main_app.main_valve_post.get():
            valve_post_pressure = self.main_app.main_valve_post.get()
            print(f"获取到调节阀后压力: {valve_post_pressure}")
        
        # 如果任一值为空，不更新
        if not valve_pre_pressure or not valve_post_pressure:
            print("阀门前压力或后压力为空，不进行更新")
            return
        
        # 找到设备3的流量信息行并更新压力值
        updated = False
        try:
            # 获取设备3正常流量值
            normal_flow = 0
            if hasattr(self.main_app, 'normal_flow') and self.main_app.normal_flow.get():
                try:
                    normal_flow = int(float(self.main_app.normal_flow.get()))
                except:
                    normal_flow = 0
            
            # 找到设备3后的流量信息行
            for i, item in enumerate(self.equipment_data):
                if item.get("序号") == "3" and "调节阀" in item.get("设备名称", ""):
                    # 找到设备3后，检查下一行是否包含流量信息
                    if i + 1 < len(self.equipment_data):
                        flow_info_row = self.equipment_data[i + 1]
                        if "Qnor=" in flow_info_row.get("型号及规格", ""):
                            current_spec = flow_info_row.get("型号及规格", "")
                            # 更新整行，保留流量值，替换压力值
                            flow_info_row["型号及规格"] = f"Qnor={normal_flow}Nm³/h 阀前压力P1={valve_pre_pressure}MPa 阀后压力P2={valve_post_pressure}MPa"
                            print(f"设备3压力信息从 {current_spec} 更新为 {flow_info_row['型号及规格']}")
                            updated = True
                            break
        except Exception as e:
            print(f"更新阀门压力信息时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        if not updated:
            print("未找到设备3的流量信息行")
            return
        
        # 刷新设备表显示
        self.refresh_equipment_table()
        print("设备表压力信息已更新")

    def update_equipment4_with_furnace_count(self, furnace_count):
        """
        根据小炉数量更新设备序号，从4开始递增
        
        Args:
            furnace_count: 小炉数量
        """
        print(f"更新小炉设备数量: {furnace_count}")
        
        # 检查是否已经有设备数据
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            print("设备数据不存在，尝试初始化设备数据")
            # 尝试初始化设备数据
            self.show_equipment_list(show_window=False)
            # 如果初始化后仍然没有数据，则返回
            if not hasattr(self, 'equipment_data') or not self.equipment_data:
                print("无法初始化设备数据，无法更新设备")
                return
        
        try:
            # 查找设备3的位置
            device3_idx = None
            for i, item in enumerate(self.equipment_data):
                if item.get("序号") == "3":
                    device3_idx = i
                    break
            
            if device3_idx is None:
                print("未找到设备3，无法确定起始位置")
                return
            
            # 统计设备3后面的附加行数量
            additional_rows = 0
            i = device3_idx + 1
            while i < len(self.equipment_data) and self.equipment_data[i].get("序号") == "":
                additional_rows += 1
                i += 1
            
            # 计算设备4应该开始的位置
            start_pos = device3_idx + additional_rows + 1
            
            # 统计现有设备数量(序号为4及以上的)
            existing_devices = []
            i = start_pos
            while i < len(self.equipment_data):
                if self.equipment_data[i].get("序号") not in ["", None]:
                    existing_devices.append(i)
                    # 跳过该设备的附加行
                    i += 1
                    while i < len(self.equipment_data) and self.equipment_data[i].get("序号") == "":
                        i += 1
                else:
                    i += 1
            
            # 现有设备的数量
            existing_count = len(existing_devices)
            
            # 创建设备模板（用于添加新设备）
            equipment_template = [
                # 主条目
                {
                    "序号": "4",  # 将在使用时更新
                    "位号": "R-02(A~B)",  # 将在使用时更新
                    "设备名称": "顶部导向型单座调节阀",
                    "型号及规格": "501T-5200LA PN16 65AX50A Cv=45",
                    "单位": "台",
                    "数量": "2",  # 每个小炉2个阀门
                    "单重": "",
                    "总重": "",
                    "设备来源": "工装自控工程(无锡)有限公司",
                    "设备图号": "",
                    "备注": "小炉1枪前支管调节 带手轮"  # 将在使用时更新
                },
                # 添加三个附加行
                {
                    "序号": "",
                    "位号": "",
                    "设备名称": "",
                    "型号及规格": "Qnor=193~503Nm³/h 阀前压力P1=0.15MPa 阀后压力P2=0.06MPa",
                    "单位": "",
                    "数量": "",
                    "单重": "",
                    "总重": "",
                    "设备来源": "",
                    "设备图号": "",
                    "备注": "配成套法兰及紧固、密封件(见说明)"
                },
                {
                    "序号": "",
                    "位号": "",
                    "设备名称": "",
                    "型号及规格": "软密封 介质：天然气 阀体材质：碳钢 等百分比特性 故障关",
                    "单位": "",
                    "数量": "",
                    "单重": "",
                    "总重": "",
                    "设备来源": "",
                    "设备图号": "",
                    "备注": "阀门操作位置：右侧操作"
                },
                {
                    "序号": "",
                    "位号": "",
                    "设备名称": "附：电气阀门定位器(防爆型)",
                    "型号及规格": "EPB800系列 4~20mA.DC",
                    "单位": "",
                    "数量": "",
                    "单重": "",
                    "总重": "",
                    "设备来源": "",
                    "设备图号": "",
                    "备注": ""
                }
            ]
            
            # 获取小炉数据，用于比较平均热负荷和浮动值
            furnace_data = []
            if hasattr(self.main_app, 'furnace_data'):
                furnace_data = self.main_app.furnace_data
            
            # 创建小炉分组，根据平均热负荷和浮动值
            furnace_groups = {}
            for i in range(min(furnace_count, len(furnace_data))):
                heat_load = furnace_data[i]['heat_load'].get() if i < len(furnace_data) else ""
                float_value = furnace_data[i]['float_value'].get() if i < len(furnace_data) else ""
                
                # 创建组键
                group_key = f"{heat_load}_{float_value}"
                
                if group_key not in furnace_groups:
                    furnace_groups[group_key] = []
                
                furnace_groups[group_key].append(i)
            
            print(f"小炉分组情况: {furnace_groups}")
            
            # 根据分组创建设备
            next_device_num = 4  # 设备序号从4开始
            position_num = 2     # 位号中的数字从02开始
            device_indices = []  # 存储已创建的设备索引
            
            # 先删除所有现有设备
            to_delete = []
            for i in range(existing_count):
                main_idx = existing_devices[i]
                
                # 找出该设备的所有附加行
                j = main_idx + 1
                while j < len(self.equipment_data) and self.equipment_data[j].get("序号") == "":
                    to_delete.append(j)
                    j += 1
                
                # 添加主条目索引
                to_delete.append(main_idx)
            
            # 从高到低删除
            for idx in sorted(to_delete, reverse=True):
                del self.equipment_data[idx]
            
            # 为每个分组创建设备
            for group_key, furnace_indices in furnace_groups.items():
                # 创建一个设备条目及其附加行
                for template_idx, template_item in enumerate(equipment_template):
                    new_item = template_item.copy()
                    
                    # 只更新主条目（第一个条目）的序号、位号和备注
                    if template_idx == 0:
                        new_item["序号"] = str(next_device_num)
                        
                        # 计算位号范围 - 每个设备从A开始重新编号
                        # 每个小炉有2个阀门，所以字母范围是从A到B，或者更多小炉时到更多字母
                        if len(furnace_indices) == 1:
                            # 单个小炉，位号是(A~B)
                            new_item["位号"] = f"R-{position_num:02d}(A~B)"
                            new_item["数量"] = "2"  # 每个小炉2个阀门
                            new_item["备注"] = f"小炉{furnace_indices[0]+1}枪前支管调节 带手轮"
                        else:
                            # 多个小炉，位号是(A~X)，X是根据小炉数量计算的字母
                            # 每个小炉2个阀门，所以最后一个字母是A + 2*小炉数量 - 1
                            last_letter = chr(65 + len(furnace_indices) * 2 - 1)
                            new_item["位号"] = f"R-{position_num:02d}(A~{last_letter})"
                            new_item["数量"] = str(len(furnace_indices) * 2)  # 每个小炉2个阀门
                            
                            # 备注中列出所有小炉编号
                            furnace_nums = [str(idx+1) for idx in furnace_indices]
                            new_item["备注"] = f"小炉{','.join(furnace_nums)}枪前支管调节 带手轮"
                
                    # 添加到设备数据
                    self.equipment_data.append(new_item)
                
                next_device_num += 1
                position_num += 1
            
            # 刷新设备表显示
            self.refresh_equipment_table()
            print(f"成功更新小炉设备，分组后的设备数量为{next_device_num-4}个")
            # 添加此行：更新气动切断球阀
            self.update_gas_shutoff_valves()
            # 更新气动切断球阀后，同步更新安全阀
            self.add_safety_valves_after_shutoff_valves()
            
            # 确保每次更新后保存项目
            if hasattr(self.main_app, 'save_project_silent'):
                self.main_app.save_project_silent()
        
        except Exception as e:
            print(f"更新小炉设备时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_small_valve_specification(self, furnace_index=None):
        """
        当小炉阀门的阀门选取管径、阀芯尺寸或C选定变化时，更新设备表中的型号规格
        
        Args:
            furnace_index: 小炉索引，如果为None则更新所有小炉阀门
        """
        print(f"update_small_valve_specification 被调用，小炉索引: {furnace_index}")
        
        # 检查是否已经有设备数据
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            print("设备数据不存在，无法更新小炉阀门规格")
            return
        
        # 获取小炉数据
        if not hasattr(self.main_app, 'small_valve_persistent_data'):
            print("主应用中没有小炉阀门数据")
            return
            
        small_valve_data = self.main_app.small_valve_persistent_data
        if not small_valve_data:
            print("小炉阀门数据为空")
            return
        
        # 确定需要更新的小炉索引列表
        indexes_to_update = []
        if furnace_index is not None:
            # 只更新指定小炉
            if 0 <= furnace_index < len(small_valve_data):
                indexes_to_update = [furnace_index]
            else:
                print(f"小炉索引 {furnace_index} 超出范围")
                return
        else:
            # 更新所有小炉
            indexes_to_update = list(range(len(small_valve_data)))
        
        # 查找设备表中所有的顶部导向型单座调节阀
        small_valve_devices = []
        for i, item in enumerate(self.equipment_data):
            if item.get("序号") and "顶部导向型单座调节阀" in item.get("设备名称", ""):
                small_valve_devices.append((i, item))
        
        if not small_valve_devices:
            print("设备表中没有找到小炉阀门")
            return
        
        # 对每个需要更新的小炉索引，更新其对应的设备规格
        for idx in indexes_to_update:
            if idx >= len(small_valve_data):
                continue
                
            valve_data = small_valve_data[idx]
            
            # 获取阀门管径、阀芯尺寸和C选定值
            valve_diameter = valve_data.get('valve_diameter', '')
            valve_core_size = valve_data.get('valve_core_size', '')
            c_selected = valve_data.get('c_selected', '')
            
            if not valve_diameter or not valve_core_size or not c_selected:
                print(f"小炉{idx+1}的阀门数据不完整，跳过更新")
                continue
            
            print(f"小炉{idx+1}阀门数据: 管径={valve_diameter}, 阀芯尺寸={valve_core_size}, C选定={c_selected}")
            
            # 查找小炉对应的设备，根据备注中的"小炉x"或"小炉x,y,z"进行匹配
            for device_idx, device in small_valve_devices:
                remark = device.get("备注", "")
                
                # 提取备注中的小炉编号
                import re
                furnace_numbers = []
                
                # 匹配"小炉1,2,3"或"小炉1"格式
                match = re.search(r'小炉(\d+(?:,\d+)*)', remark)
                if match:
                    furnace_str = match.group(1)
                    furnace_numbers = [int(x) for x in furnace_str.split(',')]
                
                # 检查当前小炉是否在这个设备的小炉列表中
                if idx + 1 in furnace_numbers:
                    # 找到了对应的设备，更新型号规格
                    current_spec = device.get("型号及规格", "")
                    print(f"找到小炉{idx+1}对应的设备，当前规格: {current_spec}")
                    
                    # 更新规格中的阀门规格部分和Cv值
                    import re
                    if "501T-5200LA" in current_spec and "PN16" in current_spec:
                        # 更新管径和阀芯尺寸
                        spec_pattern = r'(\d+)AX(\d+)A'
                        new_spec = f"{valve_diameter}AX{valve_core_size}A"
                        current_spec = re.sub(spec_pattern, new_spec, current_spec)
                        
                        # 更新Cv值
                        if "Cv=" in current_spec:
                            current_spec = re.sub(r'Cv=\d+', f'Cv={c_selected}', current_spec)
                        else:
                            current_spec = current_spec.strip() + f" Cv={c_selected}"
                        
                        print(f"更新后的规格: {current_spec}")
                        device["型号及规格"] = current_spec
                        break
        
        # 刷新设备表显示
        self.refresh_equipment_table()
        
        # 确保每次更新后保存项目
        if hasattr(self.main_app, 'save_project_silent'):
            self.main_app.save_project_silent()
        
        print("小炉阀门规格更新完成")
        # 添加此行：更新气动切断球阀
        self.update_gas_shutoff_valves()

    def load_small_valve_data(self):
        """
        加载项目时，从设备表中提取小炉调节阀数据并更新到小炉阀门计算区域
        """
        print("从设备表加载小炉调节阀数据")
        
        # 检查是否已经有设备数据
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            return
        
        # 确保small_valve_persistent_data已初始化
        if not hasattr(self.main_app, 'small_valve_persistent_data'):
            self.main_app.small_valve_persistent_data = []
        
        # 遍历设备表查找小炉调节阀
        for item in self.equipment_data:
            if "小炉" in item.get("设备名称", "") and "调节阀" in item.get("设备名称", ""):
                # 尝试从设备名称中提取小炉编号
                import re
                match = re.search(r'小炉(\d+)', item.get("设备名称", ""))
                if match:
                    furnace_index = int(match.group(1)) - 1  # 索引从0开始
                    
                    # 从规格中提取参数
                    spec = item.get("型号及规格", "")
                    print(f"小炉{furnace_index+1}调节阀规格: {spec}")
                    
                    # 提取管径、阀芯尺寸和C值
                    diameter_match = re.search(r'(\d+)AX(\d+)A', spec)
                    cv_match = re.search(r'Cv=(\d+)', spec)
                    
                    if diameter_match and cv_match:
                        valve_diameter = diameter_match.group(1)
                        valve_core_size = diameter_match.group(2)
                        c_selected = cv_match.group(1)
                        
                        print(f"提取参数: 管径={valve_diameter}, 阀芯={valve_core_size}, C选定={c_selected}")
                        
                        # 确保small_valve_persistent_data有足够的元素
                        while len(self.main_app.small_valve_persistent_data) <= furnace_index:
                            self.main_app.small_valve_persistent_data.append({})
                        
                        # 更新数据
                        self.main_app.small_valve_persistent_data[furnace_index]['valve_diameter'] = valve_diameter
                        self.main_app.small_valve_persistent_data[furnace_index]['valve_core_size'] = valve_core_size
                        self.main_app.small_valve_persistent_data[furnace_index]['c_selected'] = c_selected
                        
                        print(f"已更新小炉{furnace_index+1}的阀门参数")
        
        # 如果阀门计算器已打开，更新显示
        if hasattr(self.main_app, 'valve_calculator') and self.main_app.valve_calculator.is_open:
            self.main_app.valve_calculator.update_small_valve_display()
            print("已更新阀门计算器显示")
        # 添加此行：更新气动切断球阀
        self.update_gas_shutoff_valves()

    # 添加新方法 - 更新小炉支管调节阀压力和流量信息到设备表中的顶部导向型单座调节阀
    def update_small_valve_pressure_flow(self, *args):
        """当小炉支管调节阀前后压力或小炉正常流量变化时，更新设备表中相应的信息"""
        print("update_small_valve_pressure_flow 被调用")
        
        # 检查是否已有设备数据
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            print("设备数据不存在")
            return
        
        # 获取小炉支管调节阀前后压力和当前活跃小炉的正常流量
        branch_pre_pressure = ""
        branch_post_pressure = ""
        furnace_data = []
        
        if hasattr(self.main_app, 'branch_valve_pre') and self.main_app.branch_valve_pre.get():
            branch_pre_pressure = self.main_app.branch_valve_pre.get()
            print(f"获取到小炉支管调节阀前压力: {branch_pre_pressure}")
        else:
            # 如果没有获取到，使用默认值确保初次加载也能更新
            branch_pre_pressure = "0.15"
            print(f"使用默认小炉支管调节阀前压力: {branch_pre_pressure}")
        
        if hasattr(self.main_app, 'branch_valve_post') and self.main_app.branch_valve_post.get():
            branch_post_pressure = self.main_app.branch_valve_post.get()
            print(f"获取到小炉支管调节阀后压力: {branch_post_pressure}")
        else:
            # 如果没有获取到，使用默认值确保初次加载也能更新
            branch_post_pressure = "0.06"
            print(f"使用默认小炉支管调节阀后压力: {branch_post_pressure}")
        
        if hasattr(self.main_app, 'furnace_data'):
            furnace_data = self.main_app.furnace_data
        
        # 如果没有小炉数据，不更新
        if not furnace_data:
            print("无小炉数据，不进行更新")
            return
        
        # 找到设备表中所有顶部导向型单座调节阀并更新信息
        try:
            for i, item in enumerate(self.equipment_data):
                if item.get("序号") and "顶部导向型单座调节阀" in item.get("设备名称", ""):
                    # 找到相应阀门后，检查下一行是否包含流量和压力信息
                    if i + 1 < len(self.equipment_data) and "Qnor=" in self.equipment_data[i + 1].get("型号及规格", ""):
                        flow_info_row = self.equipment_data[i + 1]
                        current_spec = flow_info_row.get("型号及规格", "")
                        
                        # 从备注中提取小炉编号
                        import re
                        furnace_numbers = []
                        remark = item.get("备注", "")
                        match = re.search(r'小炉(\d+(?:,\d+)*)', remark)
                        if match:
                            furnace_str = match.group(1)
                            furnace_numbers = [int(x) for x in furnace_str.split(',')]
                        
                        # 如果找到了小炉编号，更新对应的流量范围
                        if furnace_numbers:
                            # 计算这些小炉的最小和最大正常流量
                            min_flow = 0
                            max_flow = 0
                            
                            for furnace_num in furnace_numbers:
                                if 0 <= furnace_num - 1 < len(furnace_data):
                                    furnace = furnace_data[furnace_num - 1]
                                    normal_flow_text = furnace['normal_flow'].cget("text")
                                    
                                    if normal_flow_text:
                                        try:
                                            normal_flow = float(normal_flow_text)
                                            if min_flow == 0 or normal_flow < min_flow:
                                                min_flow = normal_flow
                                            if normal_flow > max_flow:
                                                max_flow = normal_flow
                                        except:
                                            pass
                            
                            # 更新规格信息
                            if min_flow > 0 and max_flow > 0:
                                # 如果最小流量和最大流量相同，只显示一个值
                                if abs(min_flow - max_flow) < 0.01:
                                    flow_str = f"{int(min_flow)}"
                                else:
                                    flow_str = f"{int(min_flow)}~{int(max_flow)}"
                                
                                # 更新整行，包括流量和压力值
                                flow_info_row["型号及规格"] = f"Qnor={flow_str}Nm³/h 阀前压力P1={branch_pre_pressure}MPa 阀后压力P2={branch_post_pressure}MPa"
                                print(f"小炉调节阀信息从 {current_spec} 更新为 {flow_info_row['型号及规格']}")
        
        except Exception as e:
            print(f"更新小炉阀门信息时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # 刷新设备表显示
        self.refresh_equipment_table()
        print("设备表小炉阀门信息已更新")
    def update_gas_shutoff_valves(self):
        """
        在顶部导向型单座调节阀设备后添加气动切断球阀设备
        规则：
        1. 设备序号在顶部导向型单座调节阀后面累加
        2. 根据选取阀后管径相同的小炉进行分组
        3. 每组小炉添加一个气动切断球阀，数量为小炉数量*2
        4. 备注格式为"小炉x,y,z支管换向"
        5. 位号从S-02开始累加，格式为S-XX(A~X)
        6. 设备规格中的DN值与选取阀后管径一致
        """
        # 检查是否已有设备数据
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            print("设备数据不存在")
            return
        # 先删除现有的所有小炉气动切断球阀设备
        to_delete = []
        for i, item in enumerate(self.equipment_data):
            if item.get("序号") and "气动切断球阀" in item.get("设备名称", "") and "小炉" in item.get("备注", ""):
                # 找出该设备的所有附加行
                j = i + 1
                while j < len(self.equipment_data) and self.equipment_data[j].get("序号") == "":
                    to_delete.append(j)
                    j += 1
                to_delete.append(i)
        
        # 从高到低删除
        for idx in sorted(to_delete, reverse=True):
            del self.equipment_data[idx]
        
        # 获取小炉数据
        furnace_data = []
        if hasattr(self.main_app, 'furnace_data'):
            furnace_data = self.main_app.furnace_data
        
        # 如果没有小炉数据，不更新
        if not furnace_data:
            print("无小炉数据，不进行更新")
            return
        
        # 获取每个小炉的选取阀后管径并按照管径值分组
        furnace_groups_by_diameter = {}
        for i, furnace in enumerate(furnace_data):
            # 检查小炉是否激活
            if not hasattr(furnace, 'is_active') or furnace['is_active'].get():
                # 获取选取阀后管径
                post_diameter = ""
                if 'selected_post_diameter' in furnace and furnace['selected_post_diameter'].get():
                    post_diameter = furnace['selected_post_diameter'].get()
                else:
                    # 如果没有选取阀后管径，跳过该小炉
                    continue
                
                # 按照选取阀后管径分组
                if post_diameter not in furnace_groups_by_diameter:
                    furnace_groups_by_diameter[post_diameter] = []
                
                furnace_groups_by_diameter[post_diameter].append(i+1)  # 存储小炉编号(从1开始)
        
        print(f"按选取阀后管径分组的小炉: {furnace_groups_by_diameter}")
        
        # 找到设备表中最大的顶部导向型单座调节阀序号
        max_valve_number = 3  # 默认从3开始
        for item in self.equipment_data:
            if item.get("序号") and "顶部导向型单座调节阀" in item.get("设备名称", ""):
                try:
                    valve_number = int(item.get("序号", "0"))
                    if valve_number > max_valve_number:
                        max_valve_number = valve_number
                except ValueError:
                    continue
        
        # 为每组小炉添加对应的气动切断球阀
        next_number = max_valve_number + 1
        position_num = 2  # 位号从S-02开始
        
        # 新增的设备列表
        new_equipment_data = []
        
        # 按照管径分组创建气动切断球阀
        for post_diameter, furnace_numbers in furnace_groups_by_diameter.items():
            if not furnace_numbers:
                continue
            
            # 创建气动切断球阀
            shutoff_valve = {
                "序号": str(next_number),
                "位号": f"S-{position_num:02d}(A~{chr(65 + len(furnace_numbers) * 2 - 1)})",
                "设备名称": "气动切断球阀",
                "型号及规格": f"301K PN16 DN{post_diameter} 阀体材质：碳钢 单动作",
                "单位": "台",
                "数量": str(len(furnace_numbers) * 2),
                "单重": "",
                "总重": "",
                "设备来源": "工装自控工程(无锡)有限公司",
                "设备图号": "",
                "备注": f"小炉{','.join([str(n) for n in furnace_numbers])}支管换向"
            }
            
            # 附加行
            additional_rows = [
                {
                    "序号": "",
                    "位号": "",
                    "设备名称": "",
                    "型号及规格": "软密封 动作时间<5s 介质：天然气 故障关",
                    "单位": "",
                    "数量": "",
                    "单重": "",
                    "总重": "",
                    "设备来源": "",
                    "设备图号": "",
                    "备注": "配成套法兰及紧固、密封件，具有火灾安全结构和防静电结构及符合相应标准的火灾安全试验证书"
                },
                {
                    "序号": "",
                    "位号": "",
                    "设备名称": "二位三通电磁阀 单电控(防爆型)",
                    "型号及规格": "AC220V/50Hz",
                    "单位": "",
                    "数量": "",
                    "单重": "",
                    "总重": "",
                    "设备来源": "ASCO公司",
                    "设备图号": "",
                    "备注": ""
                },
                {
                    "序号": "",
                    "位号": "",
                    "设备名称": "双限位开关",
                    "型号及规格": "AC220V/50Hz",
                    "单位": "",
                    "数量": "",
                    "单重": "",
                    "总重": "",
                    "设备来源": "",
                    "设备图号": "",
                    "备注": ""
                }
            ]
            
            # 添加到新设备列表
            new_equipment_data.append(shutoff_valve)
            for row in additional_rows:
                new_equipment_data.append(row)
            
            next_number += 1
            position_num += 1
        
        # 将新设备添加到设备数据中
        if new_equipment_data:
            self.equipment_data.extend(new_equipment_data)
        
        # 刷新设备表显示
        self.refresh_equipment_table()
        # 更新气动切断球阀后，同步更新安全阀
        self.add_safety_valves_after_shutoff_valves()
        print("气动切断球阀设备已添加")
    def add_safety_valves_after_shutoff_valves(self):
        """
        在小炉的气动切断球阀后面添加弹簧全启封闭式安全阀
        规则：
        1. 设备序号在最后一个小炉的气动切断球阀序号上加1
        2. 型号及规格：A42Y-16C DN125/150 起跳压力：0.32MPa(可调)
        3. 其中DN125关联调节阀计算界面放散阀门的选取DN值，DN150是比选取DN大一档的DN值
        4. 起跳压力：总管调节阀后压力+0.18，随总管调节阀后压力变化实时更新
        5. 不设置设备位号
        """
        # 检查是否已有设备数据
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            print("设备数据不存在")
            return
            
        # 先删除现有的所有安全阀设备
        to_delete = []
        for i, item in enumerate(self.equipment_data):
            if item.get("序号") and "弹簧全启封闭式安全阀" in item.get("设备名称", ""):
                # 找出该设备的所有附加行
                j = i + 1
                while j < len(self.equipment_data) and self.equipment_data[j].get("序号") == "":
                    to_delete.append(j)
                    j += 1
                to_delete.append(i)
        
        # 查找所有小炉气动切断球阀并记录它们的序号
        shutoff_valves = []
        for i, item in enumerate(self.equipment_data):
            if item.get("序号") and "气动切断球阀" in item.get("设备名称", "") and "小炉" in item.get("备注", ""):
                shutoff_valves.append((i, item))
        
        # 如果没有找到气动切断球阀，不添加安全阀
        if not shutoff_valves:
            print("未找到小炉气动切断球阀，不添加安全阀")
            return
            
        # 从高到低删除
        for idx in sorted(to_delete, reverse=True):
            del self.equipment_data[idx]
        
        # 获取小炉数据
        furnace_data = []
        if hasattr(self.main_app, 'furnace_data'):
            furnace_data = self.main_app.furnace_data
        
        # 如果没有小炉数据，不更新
        if not furnace_data:
            print("无小炉数据，不进行更新")
            return
        
        # 标准DN值列表，用于获取下一级DN值
        standard_dn_values = ["15", "20", "25", "32", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300", "350", "400", "450", "500"]
        
        # 获取总管调节阀后压力
        main_valve_post_pressure = 0.1  # 默认值
        if hasattr(self.main_app, 'main_valve_post') and self.main_app.main_valve_post.get():
            try:
                main_valve_post_pressure = float(self.main_app.main_valve_post.get())
            except ValueError:
                print("总管调节阀后压力值转换失败，使用默认值0.1")
        
        # 计算起跳压力：总管调节阀后压力+0.18
        trip_pressure = main_valve_post_pressure + 0.18
        trip_pressure_str = f"{trip_pressure:.2f}"
        
        # 获取放散阀选取DN值
        selected_dn = "125"  # 默认值
        if hasattr(self.main_app, 'release_valve_dn_value') and self.main_app.release_valve_dn_value.get():
            release_valve_dn = self.main_app.release_valve_dn_value.get()
            if release_valve_dn:
                selected_dn = release_valve_dn
                print(f"已获取放散阀选取DN值: {selected_dn}")
        
        # 替换为:
        next_dn = self.get_next_dn_value(selected_dn)
        
        # 找出最后一个小炉气动切断球阀
        if not shutoff_valves:
            return
        
        # 按序号排序
        sorted_shutoff_valves = sorted(shutoff_valves, key=lambda x: int(x[1].get("序号", "0")))
        last_shutoff_valve_idx, last_shutoff_valve = sorted_shutoff_valves[-1]
        
        # 获取序号并加1
        try:
            valve_number = int(last_shutoff_valve.get("序号", "0"))
            safety_valve_number = valve_number + 1
        except ValueError:
            print(f"无法解析气动切断球阀序号: {last_shutoff_valve.get('序号')}")
            return
        
        # 创建安全阀 - 注意: 没有设备位号，也没有小炉支管备注
        safety_valve = {
            "序号": str(safety_valve_number),
            "位号": "",  # 不设置设备位号
            "设备名称": "弹簧全启封闭式安全阀",
            "型号及规格": f"A42Y-16C DN{selected_dn}/{next_dn} 起跳压力：{trip_pressure_str}MPa(可调)",
            "单位": "台",
            "数量": "1",
            "单重": "",
            "总重": "",
            "设备来源": "",
            "设备图号": "",
            "备注": "配成套法兰及紧固密封件(见说明)"  # 移除小炉支管备注
        }
        
        # 附加行
        additional_row = {
            "序号": "",
            "位号": "",
            "设备名称": "",
            "型号及规格": "介质：天然气 阀体碳钢材质",
            "单位": "",
            "数量": "",
            "单重": "",
            "总重": "",
            "设备来源": "",
            "设备图号": "",
            "备注": ""
        }
        
        # 找到最后一个切断球阀的附加行结束位置
        j = last_shutoff_valve_idx + 1
        while j < len(self.equipment_data) and self.equipment_data[j].get("序号") == "":
            j += 1
        
        # 插入安全阀和附加行
        self.equipment_data.insert(j, safety_valve)
        self.equipment_data.insert(j + 1, additional_row)
        
        # 刷新设备表显示
        self.refresh_equipment_table()
        print("安全阀设备已添加")

        # 添加安全阀后，自动添加自力式调压阀
        self.update_self_operated_pressure_valves()

    def update_safety_valve_parameters(self, *args):
        """
        更新安全阀的参数：
        1. DN值：关联调节阀计算界面放散阀门的选取DN值，下一档值自动更新
        2. 起跳压力：总管调节阀后压力+0.18，随总管调节阀后压力变化实时更新
        3. 不设置设备位号
        """
        try:
            # 检查是否有设备数据
            if not hasattr(self, 'equipment_data') or not self.equipment_data:
                return
            
            # 标准DN值列表
            standard_dn_values = ["15", "20", "25", "32", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300", "350", "400", "450", "500", "550", "600", "700"]
            
            # 获取总管调节阀后压力
            main_valve_post_pressure = 0.1  # 默认值
            if hasattr(self.main_app, 'main_valve_post') and self.main_app.main_valve_post.get():
                try:
                    main_valve_post_pressure = float(self.main_app.main_valve_post.get())
                except ValueError:
                    print("总管调节阀后压力值转换失败，使用默认值0.1")
            
            # 计算起跳压力：总管调节阀后压力+0.18
            trip_pressure = main_valve_post_pressure + 0.18
            trip_pressure_str = f"{trip_pressure:.2f}"
            
            # 获取放散阀选取DN值
            selected_dn = "125"  # 默认值
            if hasattr(self.main_app, 'release_valve_dn_value') and self.main_app.release_valve_dn_value.get():
                release_valve_dn = self.main_app.release_valve_dn_value.get()
                if release_valve_dn:
                    selected_dn = release_valve_dn
                    print(f"更新安全阀参数 - 已获取放散阀选取DN值: {selected_dn}")
            
            # 替换为:
            next_dn = self.get_next_dn_value(selected_dn)
            
            # 查找所有安全阀设备
            for i, item in enumerate(self.equipment_data):
                if item.get("序号") and "弹簧全启封闭式安全阀" in item.get("设备名称", ""):
                    # 确保没有位号
                    item["位号"] = ""
                    
                    # 更新规格中的DN值和起跳压力
                    import re
                    spec = item.get("型号及规格", "")
                    
                    # 替换DN值
                    new_spec = re.sub(r"DN\d+/\d+", f"DN{selected_dn}/{next_dn}", spec)
                    
                    # 替换起跳压力
                    new_spec = re.sub(r"起跳压力：[\d\.]+MPa", f"起跳压力：{trip_pressure_str}MPa", new_spec)
                    
                    # 更新规格
                    item["型号及规格"] = new_spec
                    
                    # 更新备注，确保不包含小炉支管信息
                    item["备注"] = "配成套法兰及紧固密封件(见说明)"
            
            # 如果设备表窗口已打开，刷新显示
            if hasattr(self, 'equipment_window') and self.equipment_window and hasattr(self, 'tree_view') and self.tree_view:
                self.refresh_equipment_table()
            
        except Exception as e:
            print(f"更新安全阀参数时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    # 确定下一级DN值的逻辑修改为:
    def get_next_dn_value(self, selected_dn):
        """获取比当前DN值大一级的DN值"""
        # 标准DN值列表
        standard_dn_values = ["15", "20", "25", "32", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300", "350", "400", "450", "500", "550", "6000", "700"]
        
        if selected_dn in standard_dn_values:
            idx = standard_dn_values.index(selected_dn)
            if idx < len(standard_dn_values) - 1:
                return standard_dn_values[idx + 1]
            else:
                # 如果是最大值，返回一个更大的值
                return str(int(selected_dn) + 100)  # 例如，如果是800，返回900
        else:
            # 如果不在标准列表中，尝试按数值计算下一档
            try:
                dn_value = int(selected_dn)
                # 找到下一个比当前值大的标准DN值
                for std_dn in standard_dn_values:
                    if int(std_dn) > dn_value:
                        return std_dn
                # 如果都没有更大的，增加100
                return str(dn_value + 100)
            except ValueError:
                # 无法解析为整数，返回原值
                return selected_dn

    def update_self_operated_pressure_valves(self):
        """
        在全启式安全阀后添加自力式调压阀设备
        规则：
        1. 设备序号为全启式安全阀序号+1
        2. 位号是最后一个小炉顶部导向型单座调节阀位号+1(去掉A~B字母)
        3. 型号及规格：801P DN20 PN16 Cv=10 阀体碳钢材质
        4. 数量：1台，设备来源：工装自控工程（无锡）有限公司
        5. 备注：关联自力式阀计算界面的自力式阀1描述内容
        6. 子行1：介质信息和最大流量
        7. 子行2：阀前压力和阀后压力
        """
        # 检查是否已有设备数据
        if not hasattr(self, 'equipment_data') or not self.equipment_data:
            print("设备数据不存在")
            return

        # 检查是否有自力式阀计算器
        if not hasattr(self.main_app, 'self_operated_valve') or not self.main_app.self_operated_valve:
            print("自力式阀计算器不存在，跳过自力式调压阀更新")
            return

        # 获取自力式阀计算器的数据
        self_valve_calculator = self.main_app.self_operated_valve

        # 先删除现有的所有自力式调压阀设备
        to_delete = []
        for i, item in enumerate(self.equipment_data):
            if item.get("序号") and "自力式调压阀" in item.get("设备名称", ""):
                # 找出该设备的所有附加行
                j = i + 1
                while j < len(self.equipment_data) and self.equipment_data[j].get("序号") == "":
                    to_delete.append(j)
                    j += 1
                to_delete.append(i)

        # 从高到低删除
        for idx in sorted(to_delete, reverse=True):
            del self.equipment_data[idx]

        # 查找全启式安全阀的序号
        safety_valve_number = None
        for item in self.equipment_data:
            if item.get("序号") and "弹簧全启封闭式安全阀" in item.get("设备名称", ""):
                try:
                    safety_valve_number = int(item.get("序号", "0"))
                    break
                except ValueError:
                    continue

        if safety_valve_number is None:
            print("未找到全启式安全阀，无法确定自力式调压阀序号")
            return

        # 查找最后一个小炉顶部导向型单座调节阀的位号
        last_valve_position = None
        max_position_num = 0

        for item in self.equipment_data:
            if item.get("序号") and "顶部导向型单座调节阀" in item.get("设备名称", ""):
                position = item.get("位号", "")
                # 提取位号中的数字部分，如R-06(A~B)中的06
                import re
                match = re.search(r'R-(\d+)', position)
                if match:
                    position_num = int(match.group(1))
                    if position_num > max_position_num:
                        max_position_num = position_num
                        last_valve_position = position

        if last_valve_position is None:
            print("未找到小炉顶部导向型单座调节阀，无法确定自力式调压阀位号")
            return

        # 计算自力式调压阀的位号（去掉A~B字母，数字+1）
        next_position_num = max_position_num + 1

        # 获取自力式阀计算器中的阀门数量
        valve_count = len(self_valve_calculator.valves_data) if hasattr(self_valve_calculator, 'valves_data') else 0

        # 如果没有阀门数据，则不添加设备
        if valve_count == 0:
            print("自力式阀计算器中没有阀门数据，跳过设备添加")
            return

        # 找到安全阀的插入位置
        safety_valve_idx = None
        for i, item in enumerate(self.equipment_data):
            if item.get("序号") and "弹簧全启封闭式安全阀" in item.get("设备名称", ""):
                safety_valve_idx = i
                break

        if safety_valve_idx is None:
            print("未找到安全阀位置")
            return

        # 找到安全阀的附加行结束位置
        j = safety_valve_idx + 1
        while j < len(self.equipment_data) and self.equipment_data[j].get("序号") == "":
            j += 1

        # 为每个自力式阀创建设备
        insert_position = j
        for valve_idx in range(1, valve_count + 1):
            # 获取该阀门的数据
            valve_data = self_valve_calculator.valves_data.get(valve_idx, {})

            # 获取描述内容
            description = valve_data.get("description", tk.StringVar()).get() if hasattr(valve_data.get("description", ""), 'get') else ""

            # 获取最大流量
            max_flow = valve_data.get("max_flow", tk.StringVar()).get() if hasattr(valve_data.get("max_flow", ""), 'get') else ""

            # 获取阀前压力
            pre_pressure = valve_data.get("pre_pressure", tk.StringVar()).get() if hasattr(valve_data.get("pre_pressure", ""), 'get') else ""

            # 获取阀后压力
            post_pressure = valve_data.get("post_pressure", tk.StringVar()).get() if hasattr(valve_data.get("post_pressure", ""), 'get') else ""

            # 获取介质
            medium = valve_data.get("medium", tk.StringVar()).get() if hasattr(valve_data.get("medium", ""), 'get') else "天然气"

            # 获取C选定值
            c_selected = valve_data.get("c_selected", tk.StringVar()).get() if hasattr(valve_data.get("c_selected", ""), 'get') else "10"

            # 获取选取DN值
            selected_dn = valve_data.get("selected_dn", tk.StringVar()).get() if hasattr(valve_data.get("selected_dn", ""), 'get') else "DN20"

            # 计算设备序号
            device_number = safety_valve_number + valve_idx

            # 计算位号
            position = f"R-{next_position_num + valve_idx - 1:02d}"

            # 创建主设备条目
            # 从selected_dn中提取DN值，如DN20中的20
            dn_number = selected_dn.replace("DN", "") if selected_dn else "20"

            main_device = {
                "序号": str(device_number),
                "位号": position,
                "设备名称": "自力式调压阀",
                "型号及规格": f"801P {selected_dn if selected_dn else 'DN20'} PN16 Cv={c_selected if c_selected else '10'} 阀体碳钢材质",
                "单位": "台",
                "数量": "1",
                "单重": "",
                "总重": "",
                "设备来源": "工装自控工程（无锡）有限公司",
                "设备图号": "",
                "备注": f"关联自力式阀计算界面的自力式阀{valve_idx}描述内容" if not description else description
            }

            # 创建子行1：介质信息和最大流量
            sub_row1 = {
                "序号": "",
                "位号": "",
                "设备名称": "",
                "型号及规格": f"介质：{medium if medium else '天然气'} Qmax={max_flow if max_flow else '300'}Nm3/h",
                "单位": "",
                "数量": "",
                "单重": "",
                "总重": "",
                "设备来源": "",
                "设备图号": "",
                "备注": "配成套法兰及紧固密封件(见说明)"
            }

            # 创建子行2：阀前压力和阀后压力
            sub_row2 = {
                "序号": "",
                "位号": "",
                "设备名称": "",
                "型号及规格": f"阀前压力：{pre_pressure if pre_pressure else '0.2'}MPa 阀后压力：{post_pressure if post_pressure else '0.06'}MPa",
                "单位": "",
                "数量": "",
                "单重": "",
                "总重": "",
                "设备来源": "",
                "设备图号": "",
                "备注": ""
            }

            # 插入设备和子行
            self.equipment_data.insert(insert_position, main_device)
            self.equipment_data.insert(insert_position + 1, sub_row1)
            self.equipment_data.insert(insert_position + 2, sub_row2)

            # 更新插入位置
            insert_position += 3

        # 刷新设备表显示
        self.refresh_equipment_table()
        print(f"已添加{valve_count}个自力式调压阀设备")