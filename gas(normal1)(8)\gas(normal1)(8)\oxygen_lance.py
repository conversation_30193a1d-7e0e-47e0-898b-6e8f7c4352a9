import tkinter as tk
from tkinter import ttk, messagebox
import math
import os
import json
from datetime import datetime
import traceback

class OxygenLanceCalculator:
    """
    0#氧枪计算器类
    封装所有与0#氧枪计算相关的功能
    """
    
    def __init__(self, parent):
        """
        初始化0#氧枪计算器
        
        参数:
            parent: 父对象，通常是GasCalculator的实例
        """
        self.parent = parent
        self.window = None  # 窗口对象，初始为None
        
        # 初始化变量
        self.init_variables()
    def get_oxygen_data(self):
        """获取氧气计算相关的所有数据"""
        oxygen_data = {}
        try:
            # 收集氧气输入参数
            oxygen_data["氧气流量(Nm³/h)"] = self.o2_flow.get()
            oxygen_data["氧气进车间压力(MPa)"] = self.o2_inlet_pressure.get()
            oxygen_data["氧气主管阀前压力(MPa)"] = self.o2_main_valve_pre.get()
            oxygen_data["氧气主管阀后压力(MPa)"] = self.o2_main_valve_post.get()
            oxygen_data["氧气支管阀前压力(MPa)"] = self.o2_branch_valve_pre.get()
            oxygen_data["氧气支管阀后压力(MPa)"] = self.o2_branch_valve_post.get()
            oxygen_data["氧气密度(kg/m³)"] = self.o2_density.get()
            oxygen_data["氧气流速(m/s)"] = self.o2_velocity.get()
            oxygen_data["氧气放散阀开启压力"] = self.o2_relief_valve_pressure.get()
            
            # 收集氧气计算结果
            oxygen_data["氧气放散阀截面积A"] = self.o2_relief_valve_area.get()
            oxygen_data["氧气主管计算管径"] = self.o2_main_calc_diameter.get()
            oxygen_data["氧气选取主管管径"] = self.o2_main_selected_diameter.get()
            oxygen_data["氧气反算主管流速"] = self.o2_main_actual_velocity.get()
            oxygen_data["氧气支管计算阀前管径"] = self.o2_branch_pre_calc_diameter.get()
            oxygen_data["氧气选取支管阀前管径"] = self.o2_branch_pre_selected_diameter.get()
            oxygen_data["氧气反算支管阀前流速"] = self.o2_branch_pre_actual_velocity.get()
            oxygen_data["氧气支管计算阀后管径"] = self.o2_branch_post_calc_diameter.get()
            oxygen_data["氧气选取支管阀后管径"] = self.o2_branch_post_selected_diameter.get()
            oxygen_data["氧气反算支管阀后流速"] = self.o2_branch_post_actual_velocity.get()
            
            # 收集氧气阀门数据
            oxygen_data["氧气主管C计"] = self.o2_main_c_calculated.get()
            oxygen_data["氧气主管C选定"] = self.o2_main_c_selected.get()
            oxygen_data["氧气主管K"] = self.o2_main_k.get()
            oxygen_data["氧气支管C计"] = self.o2_branch_c_calculated.get()
            oxygen_data["氧气支管C选定"] = self.o2_branch_c_selected.get()
            oxygen_data["氧气支管K"] = self.o2_branch_k.get()
        except Exception as e:
            print(f"获取氧气数据时出错: {str(e)}")
            traceback.print_exc()
        
        return oxygen_data

    def get_natural_gas_data(self):
        """获取天然气计算相关的所有数据"""
        ng_data = {}
        try:
            # 收集天然气输入参数
            ng_data["天然气流量(Nm³/h)"] = self.ng_flow.get()
            ng_data["天然气进车间压力(MPa)"] = self.ng_inlet_pressure.get()
            ng_data["天然气主管阀前压力(MPa)"] = self.ng_main_valve_pre.get()
            ng_data["天然气主管阀后压力(MPa)"] = self.ng_main_valve_post.get()
            ng_data["天然气支管阀前压力(MPa)"] = self.ng_branch_valve_pre.get()
            ng_data["天然气支管阀后压力(MPa)"] = self.ng_branch_valve_post.get()
            ng_data["天然气密度(kg/m³)"] = self.ng_density.get()
            ng_data["天然气流速(m/s)"] = self.ng_velocity.get()
            
            # 收集天然气计算结果
            ng_data["天然气主管计算管径"] = self.ng_main_calc_diameter.get()
            ng_data["天然气选取主管管径"] = self.ng_main_selected_diameter.get()
            ng_data["天然气反算主管流速"] = self.ng_main_actual_velocity.get()
            ng_data["天然气支管计算阀前管径"] = self.ng_branch_pre_calc_diameter.get()
            ng_data["天然气选取支管阀前管径"] = self.ng_branch_pre_selected_diameter.get()
            ng_data["天然气反算支管阀前流速"] = self.ng_branch_pre_actual_velocity.get()
            ng_data["天然气支管计算阀后管径"] = self.ng_branch_post_calc_diameter.get()
            ng_data["天然气选取支管阀后管径"] = self.ng_branch_post_selected_diameter.get()
            ng_data["天然气反算支管阀后流速"] = self.ng_branch_post_actual_velocity.get()
            
            # 收集天然气阀门数据
            ng_data["天然气主管C计"] = self.ng_main_c_calculated.get()
            ng_data["天然气主管C选定"] = self.ng_main_c_selected.get()
            ng_data["天然气主管K"] = self.ng_main_k.get()
            ng_data["天然气支管C计"] = self.ng_branch_c_calculated.get()
            ng_data["天然气支管C选定"] = self.ng_branch_c_selected.get()
            ng_data["天然气支管K"] = self.ng_branch_k.get()
        except Exception as e:
            print(f"获取天然气数据时出错: {str(e)}")
            traceback.print_exc()
        
        return ng_data
            
    def init_variables(self):
        """初始化所有计算变量"""
        # 氧气相关变量
        self.o2_flow = tk.StringVar(value="")                 # 氧气流量
        self.o2_inlet_pressure = tk.StringVar(value="")       # 氧气进车间压力
        self.o2_main_valve_pre = tk.StringVar(value="")       # 氧气主管阀前压力
        self.o2_main_valve_post = tk.StringVar(value="")      # 氧气主管阀后压力
        self.o2_branch_valve_pre = tk.StringVar(value="")     # 氧气支管阀前压力
        self.o2_branch_valve_post = tk.StringVar(value="")    # 氧气支管阀后压力
        self.o2_density = tk.StringVar(value="1.43")          # 氧气密度，默认值
        self.o2_velocity = tk.StringVar(value="25")           # 氧气流速，默认值
        self.o2_relief_valve_pressure = tk.StringVar(value="") # 氧气放散阀开启压力
        
        # 氧气计算结果变量
        self.o2_relief_valve_area = tk.StringVar(value="")    # 氧气放散阀截面积
        self.o2_main_calc_diameter = tk.StringVar(value="")   # 主管计算管径
        self.o2_main_selected_diameter = tk.StringVar(value="") # 选取主管管径
        self.o2_main_actual_velocity = tk.StringVar(value="") # 反算流速
        self.o2_branch_pre_calc_diameter = tk.StringVar(value="") # 支管计算阀前管径
        self.o2_branch_pre_selected_diameter = tk.StringVar(value="") # 选取支管阀前管径
        self.o2_branch_pre_actual_velocity = tk.StringVar(value="") # 反算阀前流速
        self.o2_branch_post_calc_diameter = tk.StringVar(value="") # 支管计算阀后管径
        self.o2_branch_post_selected_diameter = tk.StringVar(value="") # 选取支管阀后管径
        self.o2_branch_post_actual_velocity = tk.StringVar(value="") # 反算阀后流速
        self.o2_main_c_calculated = tk.StringVar(value="")    # 主管C计
        self.o2_main_c_selected = tk.StringVar(value="")      # 主管C选定
        self.o2_main_k = tk.StringVar(value="")               # 主管K
        self.o2_branch_c_calculated = tk.StringVar(value="")  # 支管C计
        self.o2_branch_c_selected = tk.StringVar(value="")    # 支管C选定
        self.o2_branch_k = tk.StringVar(value="")             # 支管K

        # 天然气相关变量
        self.ng_flow = tk.StringVar(value="")                 # 天然气流量
        self.ng_inlet_pressure = tk.StringVar(value="")       # 天然气进车间压力
        self.ng_main_valve_pre = tk.StringVar(value="")       # 天然气主管阀前压力
        self.ng_main_valve_post = tk.StringVar(value="")      # 天然气主管阀后压力
        self.ng_branch_valve_pre = tk.StringVar(value="")     # 天然气支管阀前压力
        self.ng_branch_valve_post = tk.StringVar(value="")    # 天然气支管阀后压力
        self.ng_density = tk.StringVar(value="0.743")         # 天然气密度，默认值
        self.ng_velocity = tk.StringVar(value="25")           # 天然气流速，默认值
        
        # 天然气计算结果变量
        self.ng_main_calc_diameter = tk.StringVar(value="")   # 主管计算管径
        self.ng_main_selected_diameter = tk.StringVar(value="") # 选取主管管径
        self.ng_main_actual_velocity = tk.StringVar(value="") # 反算流速
        self.ng_branch_pre_calc_diameter = tk.StringVar(value="") # 支管计算阀前管径
        self.ng_branch_pre_selected_diameter = tk.StringVar(value="") # 选取支管阀前管径
        self.ng_branch_pre_actual_velocity = tk.StringVar(value="") # 反算阀前流速
        self.ng_branch_post_calc_diameter = tk.StringVar(value="") # 支管计算阀后管径
        self.ng_branch_post_selected_diameter = tk.StringVar(value="") # 选取支管阀后管径
        self.ng_branch_post_actual_velocity = tk.StringVar(value="") # 反算阀后流速
        self.ng_main_c_calculated = tk.StringVar(value="")    # 主管C计
        self.ng_main_c_selected = tk.StringVar(value="")      # 主管C选定
        self.ng_main_k = tk.StringVar(value="")               # 主管K
        self.ng_branch_c_calculated = tk.StringVar(value="")  # 支管C计
        self.ng_branch_c_selected = tk.StringVar(value="")    # 支管C选定
        self.ng_branch_k = tk.StringVar(value="")             # 支管K

    def show_calculator(self):
        """显示0#氧枪计算窗口"""
        # 如果窗口已经打开，则聚焦到该窗口
        if self.window and self.window.winfo_exists():
            self.window.focus_set()
            return
            
        # 创建顶层窗口
        self.window = tk.Toplevel(self.parent.root)
        self.window.title("0#氧枪计算")
        self.window.geometry("650x550")
        
        # 设置窗口图标
        if hasattr(self.parent, 'icon_path') and os.path.exists(self.parent.icon_path):
            self.window.iconbitmap(self.parent.icon_path)
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建选项卡控件
        tab_control = ttk.Notebook(main_frame)
        tab_control.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建两个选项卡
        oxygen_tab = ttk.Frame(tab_control)
        natural_gas_tab = ttk.Frame(tab_control)
        
        tab_control.add(oxygen_tab, text="氧气栏")
        tab_control.add(natural_gas_tab, text="天然气栏")
        
        # 创建氧气栏界面
        self.create_oxygen_tab(oxygen_tab)
        
        # 创建天然气栏界面
        self.create_natural_gas_tab(natural_gas_tab)
        
        # 从当前项目加载数据
        self.load_data()
        
        # 绑定窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
        # 窗口居中显示
        self.center_window()
    
    def create_oxygen_tab(self, parent):
        """创建氧气栏界面"""
        # 创建氧气栏框架
        oxygen_frame = ttk.LabelFrame(parent, text="氧气计算参数")
        oxygen_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建输入部分
        input_frame = ttk.Frame(oxygen_frame)
        input_frame.pack(fill="x", padx=5, pady=5)
        
        # 第一行
        row = 0
        ttk.Label(input_frame, text="氧气流量(Nm³/h):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.o2_flow, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(input_frame, text="氧气进车间压力(MPa):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.o2_inlet_pressure, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第二行
        row += 1
        ttk.Label(input_frame, text="氧气主管阀前压力(MPa):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.o2_main_valve_pre, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(input_frame, text="氧气主管阀后压力(MPa):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.o2_main_valve_post, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第三行
        row += 1
        ttk.Label(input_frame, text="氧气支管阀前压力(MPa):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.o2_branch_valve_pre, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(input_frame, text="氧气支管阀后压力(MPa):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.o2_branch_valve_post, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第四行
        row += 1
        ttk.Label(input_frame, text="氧气密度(kg/m³):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.o2_density, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(input_frame, text="氧气流速(m/s):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.o2_velocity, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第五行
        row += 1
        ttk.Label(input_frame, text="氧气放散阀开启压力(MPa):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(input_frame, textvariable=self.o2_relief_valve_pressure, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        # 创建计算按钮
        button_frame = ttk.Frame(oxygen_frame)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Button(button_frame, text="计算", command=lambda: self.calculate_oxygen(force_calc=True)).pack(side="left", padx=5)
        
        # 创建结果区域
        result_frame = ttk.LabelFrame(oxygen_frame, text="计算结果")
        result_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 结果显示 - 使用表格布局
        result_rows = [
            ["氧气放散阀截面积A:", self.o2_relief_valve_area],
            ["主管计算管径(mm):", self.o2_main_calc_diameter],
            ["选取主管管径(mm):", self.o2_main_selected_diameter],
            ["反算流速(m/s):", self.o2_main_actual_velocity],
            ["支管计算阀前管径(mm):", self.o2_branch_pre_calc_diameter],
            ["选取支管阀前管径(mm):", self.o2_branch_pre_selected_diameter],
            ["反算阀前流速(m/s):", self.o2_branch_pre_actual_velocity],
            ["支管计算阀后管径(mm):", self.o2_branch_post_calc_diameter],
            ["选取支管阀后管径(mm):", self.o2_branch_post_selected_diameter],
            ["反算阀后流速(m/s):", self.o2_branch_post_actual_velocity],
            ["主管C计:", self.o2_main_c_calculated],
            ["主管C选定:", self.o2_main_c_selected],
            ["主管K:", self.o2_main_k],
            ["支管C计:", self.o2_branch_c_calculated],
            ["支管C选定:", self.o2_branch_c_selected],
            ["支管K:", self.o2_branch_k]
        ]
        
        # 分成两列显示
        mid_point = len(result_rows) // 2
        for i, (label_text, var) in enumerate(result_rows[:mid_point]):
            ttk.Label(result_frame, text=label_text).grid(row=i, column=0, sticky="w", padx=5, pady=2)
            
            # 对于需要用户输入的字段，创建Entry控件；对于只读字段，创建带状态的Entry控件
            if var in (self.o2_main_selected_diameter, self.o2_branch_pre_selected_diameter, 
                      self.o2_branch_post_selected_diameter, self.o2_main_c_selected, self.o2_branch_c_selected):
                entry = ttk.Entry(result_frame, textvariable=var, width=15)
                entry.grid(row=i, column=1, sticky="w", padx=5, pady=2)
                # 绑定事件
                if var == self.o2_main_selected_diameter:
                    var.trace_add("write", self.calculate_o2_main_actual_velocity)
                elif var == self.o2_branch_pre_selected_diameter:
                    var.trace_add("write", self.calculate_o2_branch_pre_actual_velocity)
                elif var == self.o2_branch_post_selected_diameter:
                    var.trace_add("write", self.calculate_o2_branch_post_actual_velocity)
                elif var == self.o2_main_c_selected:
                    var.trace_add("write", self.calculate_o2_main_k)
                elif var == self.o2_branch_c_selected:
                    var.trace_add("write", self.calculate_o2_branch_k)
            else:
                ttk.Entry(result_frame, textvariable=var, state="readonly", width=15).grid(row=i, column=1, sticky="w", padx=5, pady=2)
        
        # 第二列结果
        for i, (label_text, var) in enumerate(result_rows[mid_point:]):
            ttk.Label(result_frame, text=label_text).grid(row=i, column=2, sticky="w", padx=5, pady=2)
            
            # 对于需要用户输入的字段，创建Entry控件；对于只读字段，创建带状态的Entry控件
            if var in (self.o2_main_selected_diameter, self.o2_branch_pre_selected_diameter, 
                      self.o2_branch_post_selected_diameter, self.o2_main_c_selected, self.o2_branch_c_selected):
                entry = ttk.Entry(result_frame, textvariable=var, width=15)
                entry.grid(row=i, column=3, sticky="w", padx=5, pady=2)
                # 绑定事件
                if var == self.o2_main_selected_diameter:
                    var.trace_add("write", self.calculate_o2_main_actual_velocity)
                elif var == self.o2_branch_pre_selected_diameter:
                    var.trace_add("write", self.calculate_o2_branch_pre_actual_velocity)
                elif var == self.o2_branch_post_selected_diameter:
                    var.trace_add("write", self.calculate_o2_branch_post_actual_velocity)
                elif var == self.o2_main_c_selected:
                    var.trace_add("write", self.calculate_o2_main_k)
                elif var == self.o2_branch_c_selected:
                    var.trace_add("write", self.calculate_o2_branch_k)
            else:
                ttk.Entry(result_frame, textvariable=var, state="readonly", width=15).grid(row=i, column=3, sticky="w", padx=5, pady=2)
                
        # 添加输入变量变化时的自动计算功能
        self.o2_flow.trace_add("write", self.calculate_oxygen)
        self.o2_inlet_pressure.trace_add("write", self.calculate_oxygen)
        self.o2_main_valve_pre.trace_add("write", self.calculate_oxygen)
        self.o2_main_valve_post.trace_add("write", self.calculate_oxygen)
        self.o2_branch_valve_pre.trace_add("write", self.calculate_oxygen)
        self.o2_branch_valve_post.trace_add("write", self.calculate_oxygen)
        self.o2_density.trace_add("write", self.calculate_oxygen)
        self.o2_velocity.trace_add("write", self.calculate_oxygen)
        self.o2_relief_valve_pressure.trace_add("write", self.calculate_oxygen)
    
    def create_natural_gas_tab(self, parent):
        """创建天然气栏界面"""
        # 创建天然气栏框架
        natural_gas_frame = ttk.LabelFrame(parent, text="天然气计算参数")
        natural_gas_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建输入部分
        ng_input_frame = ttk.Frame(natural_gas_frame)
        ng_input_frame.pack(fill="x", padx=5, pady=5)
        
        # 第一行
        row = 0
        ttk.Label(ng_input_frame, text="天然气流量(Nm³/h):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(ng_input_frame, textvariable=self.ng_flow, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(ng_input_frame, text="天然气进车间压力(MPa):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(ng_input_frame, textvariable=self.ng_inlet_pressure, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第二行
        row += 1
        ttk.Label(ng_input_frame, text="天然气主管阀前压力(MPa):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(ng_input_frame, textvariable=self.ng_main_valve_pre, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(ng_input_frame, text="天然气主管阀后压力(MPa):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(ng_input_frame, textvariable=self.ng_main_valve_post, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第三行
        row += 1
        ttk.Label(ng_input_frame, text="天然气支管阀前压力(MPa):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(ng_input_frame, textvariable=self.ng_branch_valve_pre, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(ng_input_frame, text="天然气支管阀后压力(MPa):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(ng_input_frame, textvariable=self.ng_branch_valve_post, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 第四行
        row += 1
        ttk.Label(ng_input_frame, text="天然气密度(kg/m³):").grid(row=row, column=0, sticky="w", padx=5, pady=5)
        ttk.Entry(ng_input_frame, textvariable=self.ng_density, width=15).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(ng_input_frame, text="天然气流速(m/s):").grid(row=row, column=2, sticky="w", padx=5, pady=5)
        ttk.Entry(ng_input_frame, textvariable=self.ng_velocity, width=15).grid(row=row, column=3, sticky="w", padx=5, pady=5)
        
        # 创建计算按钮
        button_frame = ttk.Frame(natural_gas_frame)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Button(button_frame, text="计算", command=lambda: self.calculate_natural_gas(force_calc=True)).pack(side="left", padx=5)
        
        # 创建结果区域
        ng_result_frame = ttk.LabelFrame(natural_gas_frame, text="计算结果")
        ng_result_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 结果显示 - 使用表格布局
        ng_result_rows = [
            ["主管计算管径(mm):", self.ng_main_calc_diameter],
            ["选取主管管径(mm):", self.ng_main_selected_diameter],
            ["反算主管流速(m/s):", self.ng_main_actual_velocity],
            ["支管计算阀前管径(mm):", self.ng_branch_pre_calc_diameter],
            ["选取支管阀前管径(mm):", self.ng_branch_pre_selected_diameter],
            ["反算支管阀前流速(m/s):", self.ng_branch_pre_actual_velocity],
            ["支管计算阀后管径(mm):", self.ng_branch_post_calc_diameter],
            ["选取支管阀后管径(mm):", self.ng_branch_post_selected_diameter],
            ["反算支管阀后流速(m/s):", self.ng_branch_post_actual_velocity],
            ["主管C计:", self.ng_main_c_calculated],
            ["主管C选定:", self.ng_main_c_selected],
            ["主管K:", self.ng_main_k],
            ["支管C计:", self.ng_branch_c_calculated],
            ["支管C选定:", self.ng_branch_c_selected],
            ["支管K:", self.ng_branch_k]
        ]
        
        # 分成两列显示
        mid_point = len(ng_result_rows) // 2 + (1 if len(ng_result_rows) % 2 else 0)
        for i, (label_text, var) in enumerate(ng_result_rows[:mid_point]):
            ttk.Label(ng_result_frame, text=label_text).grid(row=i, column=0, sticky="w", padx=5, pady=2)
            
            # 对于需要用户输入的字段，创建Entry控件；对于只读字段，创建带状态的Entry控件
            if var in (self.ng_main_selected_diameter, self.ng_branch_pre_selected_diameter, 
                      self.ng_branch_post_selected_diameter, self.ng_main_c_selected, self.ng_branch_c_selected):
                entry = ttk.Entry(ng_result_frame, textvariable=var, width=15)
                entry.grid(row=i, column=1, sticky="w", padx=5, pady=2)
                # 绑定事件
                if var == self.ng_main_selected_diameter:
                    var.trace_add("write", self.calculate_ng_main_actual_velocity)
                elif var == self.ng_branch_pre_selected_diameter:
                    var.trace_add("write", self.calculate_ng_branch_pre_actual_velocity)
                elif var == self.ng_branch_post_selected_diameter:
                    var.trace_add("write", self.calculate_ng_branch_post_actual_velocity)
                elif var == self.ng_main_c_selected:
                    var.trace_add("write", self.calculate_ng_main_k)
                elif var == self.ng_branch_c_selected:
                    var.trace_add("write", self.calculate_ng_branch_k)
            else:
                ttk.Entry(ng_result_frame, textvariable=var, state="readonly", width=15).grid(row=i, column=1, sticky="w", padx=5, pady=2)
        
        # 第二列结果
        for i, (label_text, var) in enumerate(ng_result_rows[mid_point:]):
            ttk.Label(ng_result_frame, text=label_text).grid(row=i, column=2, sticky="w", padx=5, pady=2)
            
            # 对于需要用户输入的字段，创建Entry控件；对于只读字段，创建带状态的Entry控件
            if var in (self.ng_main_selected_diameter, self.ng_branch_pre_selected_diameter, 
                      self.ng_branch_post_selected_diameter, self.ng_main_c_selected, self.ng_branch_c_selected):
                entry = ttk.Entry(ng_result_frame, textvariable=var, width=15)
                entry.grid(row=i, column=3, sticky="w", padx=5, pady=2)
                # 绑定事件
                if var == self.ng_main_selected_diameter:
                    var.trace_add("write", self.calculate_ng_main_actual_velocity)
                elif var == self.ng_branch_pre_selected_diameter:
                    var.trace_add("write", self.calculate_ng_branch_pre_actual_velocity)
                elif var == self.ng_branch_post_selected_diameter:
                    var.trace_add("write", self.calculate_ng_branch_post_actual_velocity)
                elif var == self.ng_main_c_selected:
                    var.trace_add("write", self.calculate_ng_main_k)
                elif var == self.ng_branch_c_selected:
                    var.trace_add("write", self.calculate_ng_branch_k)
            else:
                ttk.Entry(ng_result_frame, textvariable=var, state="readonly", width=15).grid(row=i, column=3, sticky="w", padx=5, pady=2)
        
        # 添加输入变量变化时的自动计算功能
        self.ng_flow.trace_add("write", self.calculate_natural_gas)
        self.ng_inlet_pressure.trace_add("write", self.calculate_natural_gas)
        self.ng_main_valve_pre.trace_add("write", self.calculate_natural_gas)
        self.ng_main_valve_post.trace_add("write", self.calculate_natural_gas)
        self.ng_branch_valve_pre.trace_add("write", self.calculate_natural_gas)
        self.ng_branch_valve_post.trace_add("write", self.calculate_natural_gas)
        self.ng_density.trace_add("write", self.calculate_natural_gas)
        self.ng_velocity.trace_add("write", self.calculate_natural_gas)
    
    def load_data(self):
        """从父对象加载数据到0#氧枪计算器"""
        try:
            # 首先从父对象获取当前项目的信息
            project_name = self.parent.project_name.get()
            project_code = self.parent.project_code.get()
            
            # 如果没有项目名称或代号，则无法加载
            if not project_name and not project_code:
                return
            
            # 获取历史文件路径
            history_file = self.parent.history_file
            if not os.path.exists(history_file):
                return
            
            # 读取历史记录数据
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            
            # 查找当前项目的记录
            for record in history:
                if record.get("工程名称") == project_name and record.get("工程代号") == project_code:
                    # 设置是否有0#氧枪
                    if "是否有0#氧枪" in record:
                        self.parent.has_oxygen_lance.set(record.get("是否有0#氧枪"))
                    
                    # 优先从数据字典中加载氧气数据
                    o2_data = record.get("氧枪氧气数据", {})
                    if o2_data:
                        self.o2_flow.set(o2_data.get("氧气流量(Nm³/h)", ""))
                        self.o2_inlet_pressure.set(o2_data.get("氧气进车间压力(MPa)", ""))
                        self.o2_main_valve_pre.set(o2_data.get("氧气主管阀前压力(MPa)", ""))
                        self.o2_main_valve_post.set(o2_data.get("氧气主管阀后压力(MPa)", ""))
                        self.o2_branch_valve_pre.set(o2_data.get("氧气支管阀前压力(MPa)", ""))
                        self.o2_branch_valve_post.set(o2_data.get("氧气支管阀后压力(MPa)", ""))
                        self.o2_density.set(o2_data.get("氧气密度(kg/m³)", ""))
                        self.o2_velocity.set(o2_data.get("氧气流速(m/s)", ""))
                        self.o2_relief_valve_pressure.set(o2_data.get("氧气放散阀开启压力", ""))
                        
                        # 加载氧气栏计算结果
                        self.o2_relief_valve_area.set(o2_data.get("氧气放散阀截面积A", ""))
                        self.o2_main_calc_diameter.set(o2_data.get("氧气主管计算管径", ""))
                        self.o2_main_selected_diameter.set(o2_data.get("氧气选取主管管径", ""))
                        self.o2_main_actual_velocity.set(o2_data.get("氧气反算主管流速", ""))
                        self.o2_branch_pre_calc_diameter.set(o2_data.get("氧气支管计算阀前管径", ""))
                        self.o2_branch_pre_selected_diameter.set(o2_data.get("氧气选取支管阀前管径", ""))
                        self.o2_branch_pre_actual_velocity.set(o2_data.get("氧气反算支管阀前流速", ""))
                        self.o2_branch_post_calc_diameter.set(o2_data.get("氧气支管计算阀后管径", ""))
                        self.o2_branch_post_selected_diameter.set(o2_data.get("氧气选取支管阀后管径", ""))
                        self.o2_branch_post_actual_velocity.set(o2_data.get("氧气反算支管阀后流速", ""))
                        
                        # 加载氧气栏阀门数据
                        self.o2_main_c_calculated.set(o2_data.get("氧气主管C计", ""))
                        self.o2_main_c_selected.set(o2_data.get("氧气主管C选定", ""))
                        self.o2_main_k.set(o2_data.get("氧气主管K", ""))
                        self.o2_branch_c_calculated.set(o2_data.get("氧气支管C计", ""))
                        self.o2_branch_c_selected.set(o2_data.get("氧气支管C选定", ""))
                        self.o2_branch_k.set(o2_data.get("氧气支管K", ""))
                    else:
                        # 从顶层字段中加载氧气数据（兼容旧版本）
                        self.o2_flow.set(record.get("氧枪_氧气流量(Nm³/h)", ""))
                        self.o2_inlet_pressure.set(record.get("氧枪_氧气进车间压力(MPa)", ""))
                        self.o2_main_valve_pre.set(record.get("氧枪_氧气主管阀前压力(MPa)", ""))
                        self.o2_main_valve_post.set(record.get("氧枪_氧气主管阀后压力(MPa)", ""))
                        self.o2_branch_valve_pre.set(record.get("氧枪_氧气支管阀前压力(MPa)", ""))
                        self.o2_branch_valve_post.set(record.get("氧枪_氧气支管阀后压力(MPa)", ""))
                        self.o2_density.set(record.get("氧枪_氧气密度(kg/m³)", ""))
                        self.o2_velocity.set(record.get("氧枪_氧气流速(m/s)", ""))
                        self.o2_relief_valve_pressure.set(record.get("氧枪_氧气放散阀开启压力", ""))
                        
                        # 加载氧气栏计算结果
                        self.o2_relief_valve_area.set(record.get("氧枪_氧气放散阀截面积A", ""))
                        self.o2_main_calc_diameter.set(record.get("氧枪_氧气主管计算管径", ""))
                        self.o2_main_selected_diameter.set(record.get("氧枪_氧气选取主管管径", ""))
                        self.o2_main_actual_velocity.set(record.get("氧枪_氧气反算主管流速", ""))
                        self.o2_branch_pre_calc_diameter.set(record.get("氧枪_氧气支管计算阀前管径", ""))
                        self.o2_branch_pre_selected_diameter.set(record.get("氧枪_氧气选取支管阀前管径", ""))
                        self.o2_branch_pre_actual_velocity.set(record.get("氧枪_氧气反算支管阀前流速", ""))
                        self.o2_branch_post_calc_diameter.set(record.get("氧枪_氧气支管计算阀后管径", ""))
                        self.o2_branch_post_selected_diameter.set(record.get("氧枪_氧气选取支管阀后管径", ""))
                        self.o2_branch_post_actual_velocity.set(record.get("氧枪_氧气反算支管阀后流速", ""))
                        
                        # 加载氧气栏阀门数据
                        self.o2_main_c_calculated.set(record.get("氧枪_氧气主管C计", ""))
                        self.o2_main_c_selected.set(record.get("氧枪_氧气主管C选定", ""))
                        self.o2_main_k.set(record.get("氧枪_氧气主管K", ""))
                        self.o2_branch_c_calculated.set(record.get("氧枪_氧气支管C计", ""))
                        self.o2_branch_c_selected.set(record.get("氧枪_氧气支管C选定", ""))
                        self.o2_branch_k.set(record.get("氧枪_氧气支管K", ""))
                    
                    # 优先从数据字典中加载天然气数据
                    ng_data = record.get("氧枪天然气数据", {})
                    if ng_data:
                        self.ng_flow.set(ng_data.get("天然气流量(Nm³/h)", ""))
                        self.ng_inlet_pressure.set(ng_data.get("天然气进车间压力(MPa)", ""))
                        self.ng_main_valve_pre.set(ng_data.get("天然气主管阀前压力(MPa)", ""))
                        self.ng_main_valve_post.set(ng_data.get("天然气主管阀后压力(MPa)", ""))
                        self.ng_branch_valve_pre.set(ng_data.get("天然气支管阀前压力(MPa)", ""))
                        self.ng_branch_valve_post.set(ng_data.get("天然气支管阀后压力(MPa)", ""))
                        self.ng_density.set(ng_data.get("天然气密度(kg/m³)", ""))
                        self.ng_velocity.set(ng_data.get("天然气流速(m/s)", ""))
                        
                        # 加载天然气栏计算结果
                        self.ng_main_calc_diameter.set(ng_data.get("天然气主管计算管径", ""))
                        self.ng_main_selected_diameter.set(ng_data.get("天然气选取主管管径", ""))
                        self.ng_main_actual_velocity.set(ng_data.get("天然气反算主管流速", ""))
                        self.ng_branch_pre_calc_diameter.set(ng_data.get("天然气支管计算阀前管径", ""))
                        self.ng_branch_pre_selected_diameter.set(ng_data.get("天然气选取支管阀前管径", ""))
                        self.ng_branch_pre_actual_velocity.set(ng_data.get("天然气反算支管阀前流速", ""))
                        self.ng_branch_post_calc_diameter.set(ng_data.get("天然气支管计算阀后管径", ""))
                        self.ng_branch_post_selected_diameter.set(ng_data.get("天然气选取支管阀后管径", ""))
                        self.ng_branch_post_actual_velocity.set(ng_data.get("天然气反算支管阀后流速", ""))
                        
                        # 加载天然气栏阀门数据
                        self.ng_main_c_calculated.set(ng_data.get("天然气主管C计", ""))
                        self.ng_main_c_selected.set(ng_data.get("天然气主管C选定", ""))
                        self.ng_main_k.set(ng_data.get("天然气主管K", ""))
                        self.ng_branch_c_calculated.set(ng_data.get("天然气支管C计", ""))
                        self.ng_branch_c_selected.set(ng_data.get("天然气支管C选定", ""))
                        self.ng_branch_k.set(ng_data.get("天然气支管K", ""))
                    else:
                        # 从顶层字段中加载天然气数据（兼容旧版本）
                        self.ng_flow.set(record.get("氧枪_天然气流量(Nm³/h)", ""))
                        self.ng_inlet_pressure.set(record.get("氧枪_天然气进车间压力(MPa)", ""))
                        self.ng_main_valve_pre.set(record.get("氧枪_天然气主管阀前压力(MPa)", ""))
                        self.ng_main_valve_post.set(record.get("氧枪_天然气主管阀后压力(MPa)", ""))
                        self.ng_branch_valve_pre.set(record.get("氧枪_天然气支管阀前压力(MPa)", ""))
                        self.ng_branch_valve_post.set(record.get("氧枪_天然气支管阀后压力(MPa)", ""))
                        self.ng_density.set(record.get("氧枪_天然气密度(kg/m³)", ""))
                        self.ng_velocity.set(record.get("氧枪_天然气流速(m/s)", ""))
                        
                        # 加载天然气栏计算结果
                        self.ng_main_calc_diameter.set(record.get("氧枪_天然气主管计算管径", ""))
                        self.ng_main_selected_diameter.set(record.get("氧枪_天然气选取主管管径", ""))
                        self.ng_main_actual_velocity.set(record.get("氧枪_天然气反算主管流速", ""))
                        self.ng_branch_pre_calc_diameter.set(record.get("氧枪_天然气支管计算阀前管径", ""))
                        self.ng_branch_pre_selected_diameter.set(record.get("氧枪_天然气选取支管阀前管径", ""))
                        self.ng_branch_pre_actual_velocity.set(record.get("氧枪_天然气反算支管阀前流速", ""))
                        self.ng_branch_post_calc_diameter.set(record.get("氧枪_天然气支管计算阀后管径", ""))
                        self.ng_branch_post_selected_diameter.set(record.get("氧枪_天然气选取支管阀后管径", ""))
                        self.ng_branch_post_actual_velocity.set(record.get("氧枪_天然气反算支管阀后流速", ""))
                        
                        # 加载天然气栏阀门数据
                        self.ng_main_c_calculated.set(record.get("氧枪_天然气主管C计", ""))
                        self.ng_main_c_selected.set(record.get("氧枪_天然气主管C选定", ""))
                        self.ng_main_k.set(record.get("氧枪_天然气主管K", ""))
                        self.ng_branch_c_calculated.set(record.get("氧枪_天然气支管C计", ""))
                        self.ng_branch_c_selected.set(record.get("氧枪_天然气支管C选定", ""))
                        self.ng_branch_k.set(record.get("氧枪_天然气支管K", ""))
                    
                    print("已加载0#氧枪计算数据")
                    break
        except Exception as e:
            print(f"加载0#氧枪计算数据时出错: {str(e)}")
            traceback.print_exc()
    
    def save_data(self, show_message=True):
        """保存数据到父对象的历史记录中"""
        try:
            # 备份历史文件
            if hasattr(self.parent, 'backup_history_file'):
                self.parent.backup_history_file()
            
            # 获取当前项目名称和工程代号
            project_name = self.parent.project_name.get()
            project_code = self.parent.project_code.get()
            
            # 确保项目标识不为空
            if not project_name and not project_code:
                if show_message:
                    messagebox.showwarning("警告", "请先输入工程名称或工程代号")
                return False
            
            # 读取历史数据
            history = []
            history_file = self.parent.history_file
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            # 查找并更新记录
            for record in history:
                if record.get("工程名称") == project_name and record.get("工程代号") == project_code:
                    # 准备0#氧枪计算数据
                    oxygen_lance_data = {
                        "氧气流量(Nm³/h)": self.o2_flow.get(),
                        "氧气进车间压力(MPa)": self.o2_inlet_pressure.get(),
                        "氧气主管阀前压力(MPa)": self.o2_main_valve_pre.get(),
                        "氧气主管阀后压力(MPa)": self.o2_main_valve_post.get(),
                        "氧气支管阀前压力(MPa)": self.o2_branch_valve_pre.get(),
                        "氧气支管阀后压力(MPa)": self.o2_branch_valve_post.get(),
                        "氧气密度(kg/m³)": self.o2_density.get(),
                        "氧气流速(m/s)": self.o2_velocity.get(),
                        "氧气放散阀开启压力": self.o2_relief_valve_pressure.get(),
                        "氧气放散阀截面积A": self.o2_relief_valve_area.get(),
                        "氧气主管计算管径": self.o2_main_calc_diameter.get(),
                        "氧气选取主管管径": self.o2_main_selected_diameter.get(),
                        "氧气反算主管流速": self.o2_main_actual_velocity.get(),
                        "氧气支管计算阀前管径": self.o2_branch_pre_calc_diameter.get(),
                        "氧气选取支管阀前管径": self.o2_branch_pre_selected_diameter.get(),
                        "氧气反算支管阀前流速": self.o2_branch_pre_actual_velocity.get(),
                        "氧气支管计算阀后管径": self.o2_branch_post_calc_diameter.get(),
                        "氧气选取支管阀后管径": self.o2_branch_post_selected_diameter.get(),
                        "氧气反算支管阀后流速": self.o2_branch_post_actual_velocity.get(),
                        "氧气主管C计": self.o2_main_c_calculated.get(),
                        "氧气主管C选定": self.o2_main_c_selected.get(),
                        "氧气主管K": self.o2_main_k.get(),
                        "氧气支管C计": self.o2_branch_c_calculated.get(),
                        "氧气支管C选定": self.o2_branch_c_selected.get(),
                        "氧气支管K": self.o2_branch_k.get()
                    }
                    
                    natural_gas_data = {
                        "天然气流量(Nm³/h)": self.ng_flow.get(),
                        "天然气进车间压力(MPa)": self.ng_inlet_pressure.get(),
                        "天然气主管阀前压力(MPa)": self.ng_main_valve_pre.get(),
                        "天然气主管阀后压力(MPa)": self.ng_main_valve_post.get(),
                        "天然气支管阀前压力(MPa)": self.ng_branch_valve_pre.get(),
                        "天然气支管阀后压力(MPa)": self.ng_branch_valve_post.get(),
                        "天然气密度(kg/m³)": self.ng_density.get(),
                        "天然气流速(m/s)": self.ng_velocity.get(),
                        "天然气主管计算管径": self.ng_main_calc_diameter.get(),
                        "天然气选取主管管径": self.ng_main_selected_diameter.get(),
                        "天然气反算主管流速": self.ng_main_actual_velocity.get(),
                        "天然气支管计算阀前管径": self.ng_branch_pre_calc_diameter.get(),
                        "天然气选取支管阀前管径": self.ng_branch_pre_selected_diameter.get(),
                        "天然气反算支管阀前流速": self.ng_branch_pre_actual_velocity.get(),
                        "天然气支管计算阀后管径": self.ng_branch_post_calc_diameter.get(),
                        "天然气选取支管阀后管径": self.ng_branch_post_selected_diameter.get(),
                        "天然气反算支管阀后流速": self.ng_branch_post_actual_velocity.get(),
                        "天然气主管C计": self.ng_main_c_calculated.get(),
                        "天然气主管C选定": self.ng_main_c_selected.get(),
                        "天然气主管K": self.ng_main_k.get(),
                        "天然气支管C计": self.ng_branch_c_calculated.get(),
                        "天然气支管C选定": self.ng_branch_c_selected.get(),
                        "天然气支管K": self.ng_branch_k.get()
                    }
                    
                    # 保存到记录中的数据字典
                    record["氧枪氧气数据"] = oxygen_lance_data
                    record["氧枪天然气数据"] = natural_gas_data
                    
                    # 设置是否有0#氧枪标志
                    record["是否有0#氧枪"] = "是"
                    
                    # 设置计算类型
                    if record.get("计算类型") != "压缩空气管道计算":
                        record["计算类型"] = "0#氧枪计算"
                    
                    # 同时在顶层保存关键字段，以保持与原代码的兼容性
                    for key, value in oxygen_lance_data.items():
                        record[f"氧枪_氧气{key}"] = value
                    
                    for key, value in natural_gas_data.items():
                        record[f"氧枪_天然气{key}"] = value
                    
                    # 更新时间戳
                    record["时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    
                    # 保存更新后的历史记录
                    with open(history_file, 'w', encoding='utf-8') as f:
                        json.dump(history, f, ensure_ascii=False, indent=2)
                    
                    # 更新历史记录显示
                    if hasattr(self.parent, 'update_history_display'):
                        self.parent.update_history_display()
                    
                    # 设置是否有0#氧枪的标志
                    self.parent.has_oxygen_lance.set("是")
                    
                    # 更新0#氧枪计算工具按钮状态
                    if hasattr(self.parent, 'update_oxygen_tools_state'):
                        self.parent.update_oxygen_tools_state()
                    
                    if show_message:
                        messagebox.showinfo("成功", "0#氧枪计算数据已保存")
                    
                    print("0#氧枪数据已保存")
                    return True
            
            # 如果没有找到记录，创建新记录
            new_record = {
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "工程名称": project_name,
                "工程代号": project_code,
                "项目类型": self.parent.project_type.get(),
                "计算类型": "0#氧枪计算",
                "是否有0#氧枪": "是"
            }
            
            # 准备0#氧枪计算数据
            oxygen_lance_data = {
                "氧气流量(Nm³/h)": self.o2_flow.get(),
                "氧气进车间压力(MPa)": self.o2_inlet_pressure.get(),
                "氧气主管阀前压力(MPa)": self.o2_main_valve_pre.get(),
                "氧气主管阀后压力(MPa)": self.o2_main_valve_post.get(),
                "氧气支管阀前压力(MPa)": self.o2_branch_valve_pre.get(),
                "氧气支管阀后压力(MPa)": self.o2_branch_valve_post.get(),
                "氧气密度(kg/m³)": self.o2_density.get(),
                "氧气流速(m/s)": self.o2_velocity.get(),
                "氧气放散阀开启压力": self.o2_relief_valve_pressure.get(),
                "氧气放散阀截面积A": self.o2_relief_valve_area.get(),
                "氧气主管计算管径": self.o2_main_calc_diameter.get(),
                "氧气选取主管管径": self.o2_main_selected_diameter.get(),
                "氧气反算主管流速": self.o2_main_actual_velocity.get(),
                "氧气支管计算阀前管径": self.o2_branch_pre_calc_diameter.get(),
                "氧气选取支管阀前管径": self.o2_branch_pre_selected_diameter.get(),
                "氧气反算支管阀前流速": self.o2_branch_pre_actual_velocity.get(),
                "氧气支管计算阀后管径": self.o2_branch_post_calc_diameter.get(),
                "氧气选取支管阀后管径": self.o2_branch_post_selected_diameter.get(),
                "氧气反算支管阀后流速": self.o2_branch_post_actual_velocity.get(),
                "氧气主管C计": self.o2_main_c_calculated.get(),
                "氧气主管C选定": self.o2_main_c_selected.get(),
                "氧气主管K": self.o2_main_k.get(),
                "氧气支管C计": self.o2_branch_c_calculated.get(),
                "氧气支管C选定": self.o2_branch_c_selected.get(),
                "氧气支管K": self.o2_branch_k.get()
            }
            
            natural_gas_data = {
                "天然气流量(Nm³/h)": self.ng_flow.get(),
                "天然气进车间压力(MPa)": self.ng_inlet_pressure.get(),
                "天然气主管阀前压力(MPa)": self.ng_main_valve_pre.get(),
                "天然气主管阀后压力(MPa)": self.ng_main_valve_post.get(),
                "天然气支管阀前压力(MPa)": self.ng_branch_valve_pre.get(),
                "天然气支管阀后压力(MPa)": self.ng_branch_valve_post.get(),
                "天然气密度(kg/m³)": self.ng_density.get(),
                "天然气流速(m/s)": self.ng_velocity.get(),
                "天然气主管计算管径": self.ng_main_calc_diameter.get(),
                "天然气选取主管管径": self.ng_main_selected_diameter.get(),
                "天然气反算主管流速": self.ng_main_actual_velocity.get(),
                "天然气支管计算阀前管径": self.ng_branch_pre_calc_diameter.get(),
                "天然气选取支管阀前管径": self.ng_branch_pre_selected_diameter.get(),
                "天然气反算支管阀前流速": self.ng_branch_pre_actual_velocity.get(),
                "天然气支管计算阀后管径": self.ng_branch_post_calc_diameter.get(),
                "天然气选取支管阀后管径": self.ng_branch_post_selected_diameter.get(),
                "天然气反算支管阀后流速": self.ng_branch_post_actual_velocity.get(),
                "天然气主管C计": self.ng_main_c_calculated.get(),
                "天然气主管C选定": self.ng_main_c_selected.get(),
                "天然气主管K": self.ng_main_k.get(),
                "天然气支管C计": self.ng_branch_c_calculated.get(),
                "天然气支管C选定": self.ng_branch_c_selected.get(),
                "天然气支管K": self.ng_branch_k.get()
            }
            
            # 保存到记录中的数据字典
            new_record["氧枪氧气数据"] = oxygen_lance_data
            new_record["氧枪天然气数据"] = natural_gas_data
            
            # 同时在顶层保存关键字段，以保持与原代码的兼容性
            for key, value in oxygen_lance_data.items():
                new_record[f"氧枪_氧气{key}"] = value
            
            for key, value in natural_gas_data.items():
                new_record[f"氧枪_天然气{key}"] = value
            
            history.append(new_record)
            
            # 保存更新后的历史记录
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            
            # 更新历史记录显示
            if hasattr(self.parent, 'update_history_display'):
                self.parent.update_history_display()
            
            # 设置是否有0#氧枪的标志
            self.parent.has_oxygen_lance.set("是")
            
            # 更新0#氧枪计算工具按钮状态
            if hasattr(self.parent, 'update_oxygen_tools_state'):
                self.parent.update_oxygen_tools_state()
            
            if show_message:
                messagebox.showinfo("成功", "0#氧枪计算数据已保存")
            
            print("新建项目并保存0#氧枪数据")
            return True
        except Exception as e:
            print(f"保存0#氧枪计算数据时出错: {str(e)}")
            traceback.print_exc()
            if show_message:
                messagebox.showerror("错误", f"保存数据时出错: {str(e)}")
            return False
    
    def calculate_oxygen(self, *args, force_calc=False):
        """氧气栏计算方法"""
        try:
            # 获取输入参数
            try:
                o2_flow = float(self.o2_flow.get()) if self.o2_flow.get() else 0
                o2_inlet_pressure = float(self.o2_inlet_pressure.get()) if self.o2_inlet_pressure.get() else 0
                o2_main_valve_pre = float(self.o2_main_valve_pre.get()) if self.o2_main_valve_pre.get() else 0
                o2_main_valve_post = float(self.o2_main_valve_post.get()) if self.o2_main_valve_post.get() else 0
                o2_branch_valve_pre = float(self.o2_branch_valve_pre.get()) if self.o2_branch_valve_pre.get() else 0
                o2_branch_valve_post = float(self.o2_branch_valve_post.get()) if self.o2_branch_valve_post.get() else 0
                o2_density = float(self.o2_density.get()) if self.o2_density.get() else 0
                o2_velocity = float(self.o2_velocity.get()) if self.o2_velocity.get() else 0
                o2_relief_valve_pressure = float(self.o2_relief_valve_pressure.get()) if self.o2_relief_valve_pressure.get() else 0
            except ValueError:
                # 输入格式错误，静默退出
                return
            
            # 判断是自动计算(通过trace触发)还是手动点击按钮计算
            is_auto_calc = len(args) > 0 and not force_calc
            
            # 验证输入 - 只在手动计算时显示警告
            if o2_flow == 0 or o2_inlet_pressure == 0 or o2_velocity == 0 or o2_density == 0:
                if not is_auto_calc:  # 只在手动点击计算按钮时显示警告
                    messagebox.showwarning("警告", "请输入所有必要的参数")
                return
            
            # 计算放散阀截面积A
            if o2_relief_valve_pressure > 0:
                relief_valve_area = o2_flow * o2_density / 2157.4 / 1.1 / o2_relief_valve_pressure / math.sqrt(32/313)
                self.o2_relief_valve_area.set(f"{relief_valve_area:.2f}")
            
            # 计算主管计算管径
            main_calc_diameter = 18.8 * math.sqrt((o2_flow * 313 / 2730 / (o2_inlet_pressure + 0.1)) / o2_velocity)
            self.o2_main_calc_diameter.set(f"{main_calc_diameter:.2f}")
            
            # 计算支管计算阀前管径
            branch_pre_calc_diameter = 18.8 * math.sqrt((o2_flow/2 * 313 / 2730 / (o2_branch_valve_pre + 0.1)) / o2_velocity)
            self.o2_branch_pre_calc_diameter.set(f"{branch_pre_calc_diameter:.2f}")
            
            # 计算支管计算阀后管径
            branch_post_calc_diameter = 18.8 * math.sqrt((o2_flow/2 * 313 / 2730 / (o2_branch_valve_post + 0.1)) / o2_velocity)
            self.o2_branch_post_calc_diameter.set(f"{branch_post_calc_diameter:.2f}")
            
            # 计算主管C计
            if o2_main_valve_pre > o2_main_valve_post:
                main_c_calculated = 1.167 * o2_flow * math.sqrt(o2_density * 313) / 514 / (1 + 0.46 * (o2_main_valve_pre - o2_main_valve_post) / (o2_main_valve_pre * 10 + 1)) / math.sqrt((o2_main_valve_pre - o2_main_valve_post) * (o2_main_valve_pre * 10 + 1))
                self.o2_main_c_calculated.set(f"{main_c_calculated:.2f}")
            
            # 计算支管C计
            if o2_branch_valve_pre > o2_branch_valve_post:
                branch_c_calculated = 1.167 * o2_flow/2 * math.sqrt(o2_density * 313) / 514 / (1 + 0.46 * (o2_branch_valve_pre - o2_branch_valve_post) / (o2_branch_valve_pre * 10 + 1)) / math.sqrt((o2_branch_valve_pre - o2_branch_valve_post) * (o2_branch_valve_pre * 10 + 1))
                self.o2_branch_c_calculated.set(f"{branch_c_calculated:.2f}")
            
            # 执行选定值相关的计算
            self.calculate_o2_main_actual_velocity()
            self.calculate_o2_branch_pre_actual_velocity()
            self.calculate_o2_branch_post_actual_velocity()
            self.calculate_o2_main_k()
            self.calculate_o2_branch_k()
            
            # 只在手动计算时保存数据
            if not is_auto_calc or force_calc:
                self.save_data(show_message=False)
            
        except Exception as e:
            messagebox.showerror("计算错误", f"计算过程中出现错误: {str(e)}")
            traceback.print_exc()
    
    def calculate_o2_main_actual_velocity(self, *args):
        """计算氧气主管反算流速"""
        try:
            o2_flow = float(self.o2_flow.get()) if self.o2_flow.get() else 0
            o2_inlet_pressure = float(self.o2_inlet_pressure.get()) if self.o2_inlet_pressure.get() else 0
            o2_main_selected_diameter = float(self.o2_main_selected_diameter.get()) if self.o2_main_selected_diameter.get() else 0
            
            if o2_flow > 0 and o2_inlet_pressure > 0 and o2_main_selected_diameter > 0:
                actual_velocity = (o2_flow * 313 / 2730 / (o2_inlet_pressure + 0.1)) / (o2_main_selected_diameter / 18.8) / (o2_main_selected_diameter / 18.8)
                self.o2_main_actual_velocity.set(f"{actual_velocity:.2f}")
        except Exception as e:
            print(f"计算氧气主管反算流速时出错: {str(e)}")
    
    def calculate_o2_branch_pre_actual_velocity(self, *args):
        """计算氧气支管阀前反算流速"""
        try:
            o2_flow = float(self.o2_flow.get()) if self.o2_flow.get() else 0
            o2_branch_valve_pre = float(self.o2_branch_valve_pre.get()) if self.o2_branch_valve_pre.get() else 0
            o2_branch_pre_selected_diameter = float(self.o2_branch_pre_selected_diameter.get()) if self.o2_branch_pre_selected_diameter.get() else 0
            
            if o2_flow > 0 and o2_branch_valve_pre > 0 and o2_branch_pre_selected_diameter > 0:
                actual_velocity = (o2_flow/2 * 313 / 2730 / (o2_branch_valve_pre + 0.1)) / (o2_branch_pre_selected_diameter / 18.8) / (o2_branch_pre_selected_diameter / 18.8)
                self.o2_branch_pre_actual_velocity.set(f"{actual_velocity:.2f}")
        except Exception as e:
            print(f"计算氧气支管阀前反算流速时出错: {str(e)}")
    
    def calculate_o2_branch_post_actual_velocity(self, *args):
        """计算氧气支管阀后反算流速"""
        try:
            o2_flow = float(self.o2_flow.get()) if self.o2_flow.get() else 0
            o2_branch_valve_post = float(self.o2_branch_valve_post.get()) if self.o2_branch_valve_post.get() else 0
            o2_branch_post_selected_diameter = float(self.o2_branch_post_selected_diameter.get()) if self.o2_branch_post_selected_diameter.get() else 0
            
            if o2_flow > 0 and o2_branch_valve_post > 0 and o2_branch_post_selected_diameter > 0:
                actual_velocity = (o2_flow/2 * 313 / 2730 / (o2_branch_valve_post + 0.1)) / (o2_branch_post_selected_diameter / 18.8) / (o2_branch_post_selected_diameter / 18.8)
                self.o2_branch_post_actual_velocity.set(f"{actual_velocity:.2f}")
        except Exception as e:
            print(f"计算氧气支管阀后反算流速时出错: {str(e)}")
    
    def calculate_o2_main_k(self, *args):
        """计算氧气主管K值"""
        try:
            o2_main_c_calculated = float(self.o2_main_c_calculated.get()) if self.o2_main_c_calculated.get() else 0
            o2_main_c_selected = float(self.o2_main_c_selected.get()) if self.o2_main_c_selected.get() else 0
            
            if o2_main_c_calculated > 0 and o2_main_c_selected > 0:
                k = o2_main_c_calculated / o2_main_c_selected * 100
                self.o2_main_k.set(f"{k:.2f}")
        except Exception as e:
            print(f"计算氧气主管K值时出错: {str(e)}")
    
    def calculate_o2_branch_k(self, *args):
        """计算氧气支管K值"""
        try:
            o2_branch_c_calculated = float(self.o2_branch_c_calculated.get()) if self.o2_branch_c_calculated.get() else 0
            o2_branch_c_selected = float(self.o2_branch_c_selected.get()) if self.o2_branch_c_selected.get() else 0
            
            if o2_branch_c_calculated > 0 and o2_branch_c_selected > 0:
                k = o2_branch_c_calculated / o2_branch_c_selected * 100
                self.o2_branch_k.set(f"{k:.2f}")
        except Exception as e:
            print(f"计算氧气支管K值时出错: {str(e)}")
    
    def calculate_natural_gas(self, *args, force_calc=False):
        """天然气栏计算方法"""
        try:
            # 获取输入参数
            try:
                ng_flow = float(self.ng_flow.get()) if self.ng_flow.get() else 0
                ng_inlet_pressure = float(self.ng_inlet_pressure.get()) if self.ng_inlet_pressure.get() else 0
                ng_main_valve_pre = float(self.ng_main_valve_pre.get()) if self.ng_main_valve_pre.get() else 0
                ng_main_valve_post = float(self.ng_main_valve_post.get()) if self.ng_main_valve_post.get() else 0
                ng_branch_valve_pre = float(self.ng_branch_valve_pre.get()) if self.ng_branch_valve_pre.get() else 0
                ng_branch_valve_post = float(self.ng_branch_valve_post.get()) if self.ng_branch_valve_post.get() else 0
                ng_density = float(self.ng_density.get()) if self.ng_density.get() else 0
                ng_velocity = float(self.ng_velocity.get()) if self.ng_velocity.get() else 0
            except ValueError:
                # 输入格式错误，静默退出
                return
            
            # 判断是自动计算(通过trace触发)还是手动点击按钮计算
            is_auto_calc = len(args) > 0 and not force_calc
            
            # 验证输入 - 只在手动计算时显示警告
            if ng_flow == 0 or ng_inlet_pressure == 0 or ng_velocity == 0 or ng_density == 0:
                if not is_auto_calc:  # 只在手动点击计算按钮时显示警告
                    messagebox.showwarning("警告", "请输入所有必要的参数")
                return
            
            # 计算主管计算管径
            main_calc_diameter = 18.8 * math.sqrt((ng_flow * 293 / 2730 / (ng_inlet_pressure + 0.1)) / ng_velocity)
            self.ng_main_calc_diameter.set(f"{main_calc_diameter:.2f}")
            
            # 计算支管计算阀前管径
            branch_pre_calc_diameter = 18.8 * math.sqrt((ng_flow/2 * 293 / 2730 / (ng_branch_valve_pre + 0.1)) / ng_velocity)
            self.ng_branch_pre_calc_diameter.set(f"{branch_pre_calc_diameter:.2f}")
            
            # 计算支管计算阀后管径
            branch_post_calc_diameter = 18.8 * math.sqrt((ng_flow/2 * 293 / 2730 / (ng_branch_valve_post + 0.1)) / ng_velocity)
            self.ng_branch_post_calc_diameter.set(f"{branch_post_calc_diameter:.2f}")
            
            # 计算主管C计
            if ng_main_valve_pre > ng_main_valve_post:
                main_c_calculated = 1.167 * ng_flow * math.sqrt(ng_density * 293) / 514 / (1 + 0.46 * (ng_main_valve_pre - ng_main_valve_post) / (ng_main_valve_pre * 10 + 1)) / math.sqrt((ng_main_valve_pre - ng_main_valve_post) * (ng_main_valve_pre * 10 + 1))
                self.ng_main_c_calculated.set(f"{main_c_calculated:.2f}")
            
            # 计算支管C计
            if ng_branch_valve_pre > ng_branch_valve_post:
                branch_c_calculated = 1.167 * ng_flow/2 * math.sqrt(ng_density * 293) / 514 / (1 + 0.46 * (ng_branch_valve_pre - ng_branch_valve_post) / (ng_branch_valve_pre * 10 + 1)) / math.sqrt((ng_branch_valve_pre - ng_branch_valve_post) * (ng_branch_valve_pre * 10 + 1))
                self.ng_branch_c_calculated.set(f"{branch_c_calculated:.2f}")
            
            # 执行选定值相关的计算
            self.calculate_ng_main_actual_velocity()
            self.calculate_ng_branch_pre_actual_velocity()
            self.calculate_ng_branch_post_actual_velocity()
            self.calculate_ng_main_k()
            self.calculate_ng_branch_k()
            
            # 只在手动计算时保存数据
            if not is_auto_calc or force_calc:
                self.save_data(show_message=False)
            
        except Exception as e:
            messagebox.showerror("计算错误", f"计算过程中出现错误: {str(e)}")
            traceback.print_exc()
    
    def calculate_ng_main_actual_velocity(self, *args):
        """计算天然气主管反算流速"""
        try:
            ng_flow = float(self.ng_flow.get()) if self.ng_flow.get() else 0
            ng_inlet_pressure = float(self.ng_inlet_pressure.get()) if self.ng_inlet_pressure.get() else 0
            ng_main_selected_diameter = float(self.ng_main_selected_diameter.get()) if self.ng_main_selected_diameter.get() else 0
            
            if ng_flow > 0 and ng_inlet_pressure > 0 and ng_main_selected_diameter > 0:
                actual_velocity = (ng_flow * 293 / 2730 / (ng_inlet_pressure + 0.1)) / (ng_main_selected_diameter / 18.8) / (ng_main_selected_diameter / 18.8)
                self.ng_main_actual_velocity.set(f"{actual_velocity:.2f}")
        except Exception as e:
            print(f"计算天然气主管反算流速时出错: {str(e)}")
    
    def calculate_ng_branch_pre_actual_velocity(self, *args):
        """计算天然气支管阀前反算流速"""
        try:
            ng_flow = float(self.ng_flow.get()) if self.ng_flow.get() else 0
            ng_branch_valve_pre = float(self.ng_branch_valve_pre.get()) if self.ng_branch_valve_pre.get() else 0
            ng_branch_pre_selected_diameter = float(self.ng_branch_pre_selected_diameter.get()) if self.ng_branch_pre_selected_diameter.get() else 0
            
            if ng_flow > 0 and ng_branch_valve_pre > 0 and ng_branch_pre_selected_diameter > 0:
                actual_velocity = (ng_flow/2 * 293 / 2730 / (ng_branch_valve_pre + 0.1)) / (ng_branch_pre_selected_diameter / 18.8) / (ng_branch_pre_selected_diameter / 18.8)
                self.ng_branch_pre_actual_velocity.set(f"{actual_velocity:.2f}")
        except Exception as e:
            print(f"计算天然气支管阀前反算流速时出错: {str(e)}")
    
    def calculate_ng_branch_post_actual_velocity(self, *args):
        """计算天然气支管阀后反算流速"""
        try:
            ng_flow = float(self.ng_flow.get()) if self.ng_flow.get() else 0
            ng_branch_valve_post = float(self.ng_branch_valve_post.get()) if self.ng_branch_valve_post.get() else 0
            ng_branch_post_selected_diameter = float(self.ng_branch_post_selected_diameter.get()) if self.ng_branch_post_selected_diameter.get() else 0
            
            if ng_flow > 0 and ng_branch_valve_post > 0 and ng_branch_post_selected_diameter > 0:
                actual_velocity = (ng_flow/2 * 293 / 2730 / (ng_branch_valve_post + 0.1)) / (ng_branch_post_selected_diameter / 18.8) / (ng_branch_post_selected_diameter / 18.8)
                self.ng_branch_post_actual_velocity.set(f"{actual_velocity:.2f}")
        except Exception as e:
            print(f"计算天然气支管阀后反算流速时出错: {str(e)}")
    
    def calculate_ng_main_k(self, *args):
        """计算天然气主管K值"""
        try:
            ng_main_c_calculated = float(self.ng_main_c_calculated.get()) if self.ng_main_c_calculated.get() else 0
            ng_main_c_selected = float(self.ng_main_c_selected.get()) if self.ng_main_c_selected.get() else 0
            
            if ng_main_c_calculated > 0 and ng_main_c_selected > 0:
                k = ng_main_c_calculated / ng_main_c_selected * 100
                self.ng_main_k.set(f"{k:.2f}")
        except Exception as e:
            print(f"计算天然气主管K值时出错: {str(e)}")
    
    def calculate_ng_branch_k(self, *args):
        """计算天然气支管K值"""
        try:
            ng_branch_c_calculated = float(self.ng_branch_c_calculated.get()) if self.ng_branch_c_calculated.get() else 0
            ng_branch_c_selected = float(self.ng_branch_c_selected.get()) if self.ng_branch_c_selected.get() else 0
            
            if ng_branch_c_calculated > 0 and ng_branch_c_selected > 0:
                k = ng_branch_c_calculated / ng_branch_c_selected * 100
                self.ng_branch_k.set(f"{k:.2f}")
        except Exception as e:
            print(f"计算天然气支管K值时出错: {str(e)}")
    
    def on_window_close(self):
        """窗口关闭时调用"""
        try:
            # 保存数据
            self.save_data(show_message=False)
            # 关闭窗口
            if self.window:
                self.window.destroy()
                self.window = None
        except Exception as e:
            print(f"关闭0#氧枪计算窗口时出错: {str(e)}")
            traceback.print_exc()
            if self.window:
                self.window.destroy()
                self.window = None
    
    def center_window(self):
        """使窗口在屏幕上居中显示"""
        if not self.window:
            return
            
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}') 