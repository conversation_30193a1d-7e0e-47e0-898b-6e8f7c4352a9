import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import shutil
from datetime import datetime
import traceback


class ProjectManager:
    """项目管理模块，负责项目保存、历史记录管理、设置按钮等功能"""
    
    def __init__(self, gas_calculator):
        """初始化项目管理器
        
        Args:
            gas_calculator: GasCalculator主类实例的引用
        """
        self.gas_calc = gas_calculator
        
    def backup_history_file(self):
        """创建历史文件的备份"""
        try:
            if os.path.exists(self.gas_calc.history_file):
                backup_dir = os.path.join(os.path.dirname(self.gas_calc.history_file), "备份")
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)

                today = datetime.now().strftime("%Y%m%d")
                backup_file = os.path.join(backup_dir, f"history_{today}.json")

                shutil.copy2(self.gas_calc.history_file, backup_file)
        except Exception as e:
            print(f"创建历史文件备份时出错: {str(e)}")
            
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.gas_calc.config_file):
                with open(self.gas_calc.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.gas_calc.custom_history_path = config.get('history_file_path', '')
            else:
                self.gas_calc.custom_history_path = ''
        except Exception as e:
            print(f"加载配置文件出错: {str(e)}")
            self.gas_calc.custom_history_path = ''
            
    def save_config(self):
        """保存配置文件"""
        try:
            config = {}
            if os.path.exists(self.gas_calc.config_file):
                with open(self.gas_calc.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 更新历史文件路径
            config['history_file_path'] = self.gas_calc.custom_history_path if hasattr(self.gas_calc, 'custom_history_path') else ''

            # 保存配置文件
            with open(self.gas_calc.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件出错: {str(e)}")
            
    def center_window(self, window):
        """使窗口在屏幕中居中显示"""
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f'{width}x{height}+{x}+{y}')
        
    def save_air_data(self):
        """
        保存压缩空气计算数据到主程序中
        当压缩空气窗口打开时调用此方法以确保数据不丢失
        """
        try:
            if hasattr(self.gas_calc, 'compressed_air_calculator') and self.gas_calc.compressed_air_calculator:
                # 收集当前压缩空气数据
                air_data = self.gas_calc.compressed_air_calculator.collect_data()

                # 获取当前项目标识
                project_name = self.gas_calc.project_name.get()
                project_code = self.gas_calc.project_code.get()
                current_project_id = f"{project_name}_{project_code}"

                # 初始化为字典的字典（如果需要）
                if not hasattr(self.gas_calc, 'saved_air_data'):
                    self.gas_calc.saved_air_data = {}
                elif not isinstance(self.gas_calc.saved_air_data, dict):
                    self.gas_calc.saved_air_data = {}

                # 以项目标识为键，存储压缩空气数据
                self.gas_calc.saved_air_data[current_project_id] = air_data
                print(f"压缩空气数据已保存到主程序，项目ID: {current_project_id}")
                return True
        except Exception as e:
            print(f"保存压缩空气数据时出错: {str(e)}")
            traceback.print_exc()
        return False
        
    def on_closing(self):
        """主窗口关闭事件处理"""
        try:

            # 确保压缩空气数据被保存
            if hasattr(self.gas_calc, 'compressed_air_calculator') and self.gas_calc.compressed_air_calculator:
                try:
                    self.save_air_data()
                    print("主窗口关闭前已保存压缩空气数据")
                except Exception as e:
                    print(f"保存压缩空气数据时出错: {str(e)}")

            # 关闭所有子窗口
            self.close_all_windows()

            # 销毁主窗口
            self.gas_calc.root.destroy()

        except Exception as e:
            print(f"关闭主窗口时出错: {str(e)}")
            # 强制关闭
            self.gas_calc.root.destroy()
            
    def close_all_windows(self):
        """关闭所有打开的子窗口"""
        try:
            # 遍历所有打开的窗口并关闭
            for window_name, window in list(self.gas_calc.open_windows.items()):
                try:
                    if window and window.winfo_exists():
                        window.destroy()
                        print(f"已关闭窗口: {window_name}")
                except Exception as e:
                    print(f"关闭窗口 {window_name} 时出错: {str(e)}")

        # 清空窗口列表
        self.gas_calc.open_windows.clear()

    def save_project(self, show_message=True):
        """保存项目功能"""
        try:
            # 备份历史文件
            self.backup_history_file()

            # 获取压缩空气数据
            air_data = {}
            # 获取当前项目标识
            project_name = self.gas_calc.project_name.get()
            project_code = self.gas_calc.project_code.get()
            current_project_id = f"{project_name}_{project_code}"

            # 1. 首先尝试从打开的窗口获取最新数据
            if hasattr(self.gas_calc, 'air_window') and self.gas_calc.air_window and self.gas_calc.air_window.winfo_exists():
                try:
                    # 保存当前压缩空气数据到self.saved_air_data[current_project_id]
                    self.save_air_data()
                except Exception as e:
                    print(f"同步压缩空气窗口数据出错: {str(e)}")
                    traceback.print_exc()

            # 2. 使用保存的数据或直接从计算器获取
            if hasattr(self.gas_calc, 'saved_air_data') and isinstance(self.gas_calc.saved_air_data, dict) and current_project_id in self.gas_calc.saved_air_data:
                air_data = self.gas_calc.saved_air_data[current_project_id]
                print(f"使用已保存的{current_project_id}项目的压缩空气数据")
            elif hasattr(self.gas_calc, 'compressed_air_calculator') and self.gas_calc.compressed_air_calculator:
                try:
                    air_data = self.gas_calc.compressed_air_calculator.collect_data()
                    # 确保数据与当前项目关联
                    if hasattr(self.gas_calc, 'saved_air_data'):
                        if not isinstance(self.gas_calc.saved_air_data, dict):
                            self.gas_calc.saved_air_data = {}
                        self.gas_calc.saved_air_data[current_project_id] = air_data
                    print(f"已重新获取{current_project_id}项目的压缩空气数据")
                except Exception as e:
                    print(f"获取压缩空气数据时出错: {str(e)}")
                    traceback.print_exc()


            # 收集项目数据
            project_data = {
                "工程名称": self.gas_calc.project_name.get(),
                "工程代号": self.gas_calc.project_code.get(),
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),

            }

            # 合并压缩空气数据
            if air_data:
                for key, value in air_data.items():
                    project_data[key] = value

        except Exception as e:
            print(f"保存项目数据时出错: {str(e)}")
            traceback.print_exc()
            if show_message:
                messagebox.showerror("保存失败", f"保存项目数据时出错: {str(e)}")
            return False

        # 收集其他数据
        try:
            # 获取氧枪数据
            if hasattr(self.gas_calc, 'has_oxygen_lance') and self.gas_calc.has_oxygen_lance.get() == "是":
                if hasattr(self.gas_calc, 'oxygen_lance_calculator') and self.gas_calc.oxygen_lance_calculator:
                    try:
                        o2_data, ng_data = self.gas_calc.oxygen_lance_calculator.collect_data()
                        project_data["氧枪氧气数据"] = o2_data
                        project_data["氧枪天然气数据"] = ng_data

                        # 使用"氧枪_"前缀保存氧气数据
                        for key, value in o2_data.items():
                            project_data[f"氧枪_氧气{key}"] = value

                        # 使用"氧枪_"前缀保存天然气数据
                        for key, value in ng_data.items():
                            project_data[f"氧枪_天然气{key}"] = value
                    except Exception as e:
                        print(f"收集氧枪数据时出错: {str(e)}")

            # 收集天然气数据
            if hasattr(self.gas_calc, 'has_natural_gas') and self.gas_calc.has_natural_gas.get() == "是":
                project_data["天然气总管调节阀前压力(kPa)"] = self.gas_calc.ng_main_valve_pre.get()
                project_data["天然气总管调节阀后压力(kPa)"] = self.gas_calc.ng_main_valve_post.get()
                project_data["天然气支管调节阀前压力(kPa)"] = self.gas_calc.ng_branch_valve_pre.get()
                project_data["天然气支管调节阀后压力(kPa)"] = self.gas_calc.ng_branch_valve_post.get()
                project_data["天然气支管K"] = self.gas_calc.ng_branch_k.get()

            # 收集自力式阀数据
            if hasattr(self.gas_calc, 'valve_count'):
                for i in range(1, int(self.gas_calc.valve_count.get()) + 1):
                    if hasattr(self.gas_calc, f"valve{i}_description"):
                        project_data[f"自力式阀{i}描述"] = getattr(self.gas_calc, f"valve{i}_description").get()
                        project_data[f"自力式阀{i}最大流量(Nm³/h)"] = getattr(self.gas_calc, f"valve{i}_max_flow").get()
                        project_data[f"自力式阀{i}介质"] = getattr(self.gas_calc, f"valve{i}_medium").get()
                        project_data[f"自力式阀{i}阀前压力(kPa)"] = getattr(self.gas_calc, f"valve{i}_pre_pressure").get()
                        project_data[f"自力式阀{i}阀后压力(kPa)"] = getattr(self.gas_calc, f"valve{i}_post_pressure").get()
                        project_data[f"自力式阀{i}开度K(%)"] = getattr(self.gas_calc, f"valve{i}_k_percent").get()

            # 收集小炉数据
            if hasattr(self.gas_calc, 'furnace_data'):
                for furnace in self.gas_calc.furnace_data:
                    furnace_data = {
                        "平均热负荷": furnace['heat_load'].get(),
                        "浮动值": furnace['float_value'].get(),
                        "喷枪数": furnace['nozzle_count'].get(),
                        "选取管径": furnace['selected_diameter'].get(),
                        "选取阀后管径": furnace['selected_post_diameter'].get()
                    }
                    project_data[f"小炉{len(self.gas_calc.furnace_data)}数据"] = furnace_data

        except Exception as e:
            print(f"收集其他数据时出错: {str(e)}")

        # 保存到历史文件
        try:
            # 读取现有历史记录
            if os.path.exists(self.gas_calc.history_file):
                with open(self.gas_calc.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            else:
                history = []

            # 检查是否存在相同工程名称和工程代号的记录
            found_index = -1
            project_name = self.gas_calc.project_name.get()
            project_code = self.gas_calc.project_code.get()

            for i, record in enumerate(history):
                if record.get("工程名称") == project_name:
                    found_index = i
                    break
                if project_code and record.get("工程代号") == project_code:
                    found_index = i
                    break

            # 更新或添加记录
            if found_index >= 0:
                if show_message:
                    result = messagebox.askyesno("确认", f"工程名称 '{project_name}' 已存在，是否覆盖？")
                    if not result:
                        return False
                history[found_index] = project_data
            else:
                history.append(project_data)

            # 保存更新后的历史记录
            with open(self.gas_calc.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

            # 更新历史记录显示
            if hasattr(self.gas_calc, 'update_history_display'):
                self.gas_calc.update_history_display()

            if show_message:
                messagebox.showinfo("保存成功", f"项目 '{project_name}' 已保存到历史记录")
            return True

        except Exception as e:
            print(f"保存到历史文件时出错: {str(e)}")
            if show_message:
                messagebox.showerror("保存失败", f"保存到历史文件时出错: {str(e)}")
            return False

    def save_project_silent(self):
        """静默保存项目，不显示消息框"""
        return self.save_project(show_message=False)

    def load_history(self):
        """加载历史记录"""
        try:
            if os.path.exists(self.gas_calc.history_file):
                with open(self.gas_calc.history_file, 'r', encoding='utf-8') as f:
                    self.gas_calc.history = json.load(f)
            else:
                self.gas_calc.history = []
        except Exception as e:
            print(f"加载历史记录时出错: {str(e)}")
            self.gas_calc.history = []

    def update_history_display(self):
        """更新历史记录显示"""
        try:
            if hasattr(self.gas_calc, 'history_tree') and self.gas_calc.history_tree:
                # 清空现有项目
                for item in self.gas_calc.history_tree.get_children():
                    self.gas_calc.history_tree.delete(item)

                # 添加历史记录
                for record in self.gas_calc.history:
                    time_str = record.get("时间", "")
                    project_name = record.get("工程名称", "")
                    project_code = record.get("工程代号", "")

                    self.gas_calc.history_tree.insert("", "end", values=(time_str, project_name, project_code))

        except Exception as e:
            print(f"更新历史记录显示时出错: {str(e)}")

    def delete_record(self):
        """删除选中的历史记录"""
        try:
            if not hasattr(self.gas_calc, 'history_tree') or not self.gas_calc.history_tree:
                return

            selected = self.gas_calc.history_tree.selection()
            if not selected:
                messagebox.showinfo("提示", "请先选择要删除的记录")
                return

            # 确认删除
            result = messagebox.askyesno("确认删除", "确定要删除选中的记录吗？")
            if not result:
                return

            # 获取选中记录的信息
            values = self.gas_calc.history_tree.item(selected[0], "values")
            if not values or len(values) < 2:
                return

            # 从历史数据中删除匹配的记录
            for i, record in enumerate(self.gas_calc.history):
                if (record.get("时间") == values[0] and
                    record.get("工程名称") == values[1] and
                    record.get("工程代号") == values[2]):

                    del self.gas_calc.history[i]
                    break

            # 保存更新后的历史记录
            with open(self.gas_calc.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.gas_calc.history, f, ensure_ascii=False, indent=2)

            # 更新显示
            self.update_history_display()
            messagebox.showinfo("删除成功", "记录已删除")

        except Exception as e:
            messagebox.showerror("删除失败", f"删除记录时出错: {str(e)}")

    def search_history(self):
        """搜索历史记录"""
        try:
            if not hasattr(self.gas_calc, 'search_var') or not hasattr(self.gas_calc, 'history_tree'):
                return

            search_text = self.gas_calc.search_var.get().lower()

            # 清空现有项目
            for item in self.gas_calc.history_tree.get_children():
                self.gas_calc.history_tree.delete(item)

            # 添加匹配的历史记录
            for record in self.gas_calc.history:
                time_str = record.get("时间", "")
                project_name = record.get("工程名称", "")
                project_code = record.get("工程代号", "")

                # 检查是否匹配搜索条件
                if (search_text in time_str.lower() or
                    search_text in project_name.lower() or
                    search_text in project_code.lower()):

                    self.gas_calc.history_tree.insert("", "end", values=(time_str, project_name, project_code))

        except Exception as e:
            print(f"搜索历史记录时出错: {str(e)}")

    def clear_search(self):
        """清空搜索"""
        try:
            if hasattr(self.gas_calc, 'search_var'):
                self.gas_calc.search_var.set("")
            self.update_history_display()
        except Exception as e:
            print(f"清空搜索时出错: {str(e)}")

    def create_side_menu(self):
        """创建侧边菜单栏，按照项目管理、计算工具、系统选项分类"""
        # 创建侧边栏容器框架
        side_menu = ttk.LabelFrame(self.gas_calc.main_container, text="功能导航")

        # 将侧边栏添加到主容器的最左侧
        self.gas_calc.main_container.insert(0, side_menu, weight=1)  # 侧边栏占1份宽度

        # 创建垂直布局来容纳菜单项
        menu_container = ttk.Frame(side_menu)
        menu_container.pack(fill="both", expand=True, padx=5, pady=5)

        # 1. 项目管理分组
        project_manage = ttk.LabelFrame(menu_container, text="项目管理")
        project_manage.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(project_manage, text="保存项目", command=self.gas_calc.save_project).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(project_manage, text="导入项目json", command=self.gas_calc.import_project_json).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(project_manage, text="导出项目json", command=self.gas_calc.export_project_json).pack(fill="x", padx=5, pady=2)

        # 添加设备表按钮
        ttk.Button(project_manage, text="设备表", command=self.gas_calc.equipment_manager.show_equipment_list).pack(fill=tk.X, padx=5, pady=3)

        # 2. 计算工具分组
        calc_tools = ttk.LabelFrame(menu_container, text="计算工具")
        calc_tools.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(calc_tools, text="刷新计算", command=self.gas_calc.refresh_all_calculations).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(calc_tools, text="压缩空气计算", command=self.gas_calc.show_compressed_air_calculator).pack(fill=tk.X, padx=5, pady=3)

        ttk.Button(calc_tools, text="调节阀计算", command=self.gas_calc.show_valve_calculator).pack(fill=tk.X, padx=5, pady=3)
         # 添加自力式阀计算按钮
        ttk.Button(calc_tools, text="自力式阀计算", command=self.gas_calc.show_self_operated_valve_calculator).pack(fill=tk.X, padx=5, pady=3)

        # 创建0#氧枪和全氧窑氧气管道计算按钮，但初始状态设为禁用
        self.gas_calc.oxygen_lance_button = ttk.Button(calc_tools, text="0#氧枪计算", command=self.gas_calc.show_oxygen_lance_calculator, state="disabled")
        self.gas_calc.oxygen_lance_button.pack(fill=tk.X, padx=5, pady=3)

        self.gas_calc.oxygen_pipe_button = ttk.Button(calc_tools, text="全氧窑氧气管道计算", command=self.gas_calc.show_oxygen_pipe_calculator, state="disabled")
        self.gas_calc.oxygen_pipe_button.pack(fill=tk.X, padx=5, pady=3)
        # 3. 系统选项分组
        system_options = ttk.LabelFrame(menu_container, text="系统选项")
        system_options.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(system_options, text="设置", command=self.show_settings_window).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(system_options, text="历史记录管理", command=self.show_history_manager).pack(fill=tk.X, padx=5, pady=3)

    def show_history_manager(self):
        """显示历史记录管理窗口，提供更丰富的历史记录管理功能"""
        # 如果窗口已经打开，则聚焦到该窗口
        if 'history_window' in self.gas_calc.open_windows and self.gas_calc.open_windows['history_window'].winfo_exists():
            self.gas_calc.open_windows['history_window'].focus_set()
            return
        # 创建顶层窗口
        history_window = tk.Toplevel(self.gas_calc.root)
        history_window.title("历史记录管理")
        history_window.geometry("900x600")  # 增加窗口宽度以容纳所有搜索框
        #history_window.transient(self.gas_calc.root)
        #history_window.grab_set()
        # 【新增】将窗口添加到管理系统
        self.gas_calc.open_windows['history_window'] = history_window
        # 设置窗口图标
        if hasattr(self.gas_calc, 'icon_path') and os.path.exists(self.gas_calc.icon_path):
            history_window.iconbitmap(self.gas_calc.icon_path)

        # 创建主框架
        main_frame = ttk.Frame(history_window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        # 初始化历史记录变量
        history = []

        # 创建搜索区域
        search_frame = ttk.LabelFrame(main_frame, text="搜索条件")
        search_frame.pack(fill="x", padx=5, pady=5)

        # 创建搜索条件输入控件 - 第一行
        ttk.Label(search_frame, text="工程名称:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        name_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=name_var, width=15).grid(row=0, column=1, padx=5, pady=5, sticky="w")

        ttk.Label(search_frame, text="工程代号:").grid(row=0, column=2, padx=5, pady=5, sticky="w")
        code_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=code_var, width=15).grid(row=0, column=3, padx=5, pady=5, sticky="w")

        ttk.Label(search_frame, text="项目类型:").grid(row=0, column=4, padx=5, pady=5, sticky="w")
        type_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=type_var, width=15).grid(row=0, column=5, padx=5, pady=5, sticky="w")
        # 添加吨位搜索
        ttk.Label(search_frame, text="吨位:").grid(row=0, column=6, padx=5, pady=5, sticky="w")
        capacity_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=capacity_var, width=15).grid(row=0, column=7, padx=5, pady=5, sticky="w")

        # 第二行 - 日期范围和小炉数
        ttk.Label(search_frame, text="日期范围:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        date_from_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=date_from_var, width=15).grid(row=1, column=1, padx=5, pady=5, sticky="w")

        ttk.Label(search_frame, text="至").grid(row=1, column=2, padx=5, pady=5, sticky="w")
        date_to_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=date_to_var, width=15).grid(row=1, column=3, padx=5, pady=5, sticky="w")


        # 将小炉数和一窑几线放在最右侧
        ttk.Label(search_frame, text="小炉数:").grid(row=1, column=4, padx=5, pady=5, sticky="w")
        furnace_count_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=furnace_count_var, width=15).grid(row=1, column=5, padx=5, pady=5, sticky="w")

        ttk.Label(search_frame, text="一窑几线:").grid(row=1, column=6, padx=5, pady=5, sticky="w")
        line_count_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=line_count_var, width=15).grid(row=1, column=7, padx=5, pady=5, sticky="w")


       # 将按钮放在第三行
        btn_frame = ttk.Frame(search_frame)
        btn_frame.grid(row=2, column=5, columnspan=3, padx=5, pady=5, sticky="e")

        def search_records():
            """执行高级搜索功能"""
            # 从历史文件中读取记录
            history = []
            if os.path.exists(self.gas_calc.history_file):
                try:
                    with open(self.gas_calc.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                except Exception as e:
                    messagebox.showerror("错误", f"读取历史记录时出错: {str(e)}")
                    return

            # 过滤记录
            filtered = []
            for record in history:
                # 检查各个搜索条件
                name_match = True
                if name_var.get():
                    name_match = name_var.get().lower() in str(record.get("工程名称", "")).lower()

                code_match = True
                if code_var.get():
                    code_match = code_var.get().lower() in str(record.get("工程代号", "")).lower()

                type_match = True
                if type_var.get():
                    type_match = type_var.get().lower() in str(record.get("项目类型", "")).lower()
                # 添加吨位搜索条件
                capacity_match = True
                if capacity_var.get():
                    capacity_match = capacity_var.get() in str(record.get("吨位", ""))


                # 小炉数搜索条件
                furnace_count_match = True
                if furnace_count_var.get():
                    # 将记录中的小炉数转为字符串进行包含比较
                    furnace_count_match = furnace_count_var.get() in str(record.get("小炉数", ""))

                # 一窑几线搜索条件
                line_count_match = True
                if line_count_var.get():
                    line_count_match = line_count_var.get() in str(record.get("一窑几线", ""))

                # 日期范围搜索条件
                date_match = True
                if date_from_var.get() or date_to_var.get():
                    record_date = record.get("时间", "")
                    if record_date:
                        try:
                            # 提取日期部分（假设格式为 "YYYY-MM-DD HH:MM:SS"）
                            record_date_str = record_date.split(" ")[0]

                            if date_from_var.get():
                                date_match = date_match and record_date_str >= date_from_var.get()
                            if date_to_var.get():
                                date_match = date_match and record_date_str <= date_to_var.get()
                        except:
                            date_match = False

                # 如果所有条件都匹配，则添加到过滤结果中
                if (name_match and code_match and type_match and capacity_match and
                    furnace_count_match and line_count_match and date_match):
                    filtered.append(record)

            # 更新显示
            update_tree_display(filtered)
            status_var.set(f"找到 {len(filtered)} 条记录")

        def load_all_records():
            """加载所有记录"""
            history = []
            if os.path.exists(self.gas_calc.history_file):
                try:
                    with open(self.gas_calc.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                except Exception as e:
                    messagebox.showerror("错误", f"读取历史记录时出错: {str(e)}")
                    return

            update_tree_display(history)
            status_var.set(f"共 {len(history)} 条记录")

        def update_tree_display(records):
            """更新树形视图显示"""
            # 清空现有项目
            for item in history_tree.get_children():
                history_tree.delete(item)

            # 添加记录
            for record in records:
                time_str = record.get("时间", "")
                project_name = record.get("工程名称", "")
                project_code = record.get("工程代号", "")
                project_type = record.get("项目类型", "")
                capacity = record.get("吨位", "")
                furnace_count = record.get("小炉数", "")
                line_count = record.get("一窑几线", "")

                history_tree.insert("", "end", values=(time_str, project_name, project_code,
                                                     project_type, capacity, furnace_count, line_count))

        ttk.Button(btn_frame, text="搜索", command=search_records).pack(side="right", padx=5)
        ttk.Button(btn_frame, text="重置", command=lambda: [
            name_var.set(""),
            code_var.set(""),
            type_var.set(""),
            date_from_var.set(""),
            date_to_var.set(""),
            furnace_count_var.set(""),
            line_count_var.set(""),
            capacity_var.set(""),
            load_all_records()
        ]).pack(side="right", padx=5)



        # 创建结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="搜索结果")
        result_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 创建树形视图以显示结果 - 移除计算类型列
        columns = ("时间", "工程名称", "工程代号", "项目类型",  "吨位", "小炉数", "一窑几线")
        history_tree = ttk.Treeview(result_frame, columns=columns, show="headings")

        # 设置列标题和宽度，确保均匀分布
        total_width = result_frame.winfo_width() or 780  # 如果winfo_width返回0，使用默认值
        column_width = total_width // len(columns)

        for col in columns:
            history_tree.heading(col, text=col)
            history_tree.column(col, width=column_width, anchor="center")  # 居中显示内容

        # 添加滚动条
        scroll_y = ttk.Scrollbar(result_frame, orient="vertical", command=history_tree.yview)
        scroll_x = ttk.Scrollbar(result_frame, orient="horizontal", command=history_tree.xview)
        history_tree.configure(yscrollcommand=scroll_y.set, xscrollcommand=scroll_x.set)

        scroll_y.pack(side="right", fill="y")
        scroll_x.pack(side="bottom", fill="x")
        history_tree.pack(side="left", fill="both", expand=True)

        # 添加状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill="x", padx=5, pady=5)

        status_var = tk.StringVar(value="准备就绪")
        ttk.Label(status_frame, textvariable=status_var).pack(side="left")

        # 添加操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", padx=5, pady=5)

        # 初始加载所有记录
        load_all_records()
