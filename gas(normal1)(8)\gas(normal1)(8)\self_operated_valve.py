import math
import json
import os
import tkinter as tk
from tkinter import ttk

class SelfOperatedValve:
    """
    自力式阀计算类
    封装所有自力式阀相关的计算和数据管理功能
    """
    def __init__(self, parent=None):
        """
        初始化自力式阀计算类
        
        Args:
            parent: 父级对象，通常是GasCalculator实例
        """
        self.parent = parent
        self.valve_tabs = []  # 保存所有自力式阀标签页信息
        self.valve_tab_count = 0
        
        # 从父对象中获取history_file路径，如果不存在则创建默认路径
        if parent and hasattr(parent, 'history_file'):
            self.history_file = parent.history_file
        elif parent and hasattr(parent, 'app_data_dir'):
            self.history_file = os.path.join(parent.app_data_dir, 'history.json')
        else:
            # 默认路径
            app_data_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'GasCalculator')
            if not os.path.exists(app_data_dir):
                os.makedirs(app_data_dir)
            self.history_file = os.path.join(app_data_dir, 'history.json')
        
        # 创建变量字典，存储每个阀门的所有参数
        self.valves_data = {}
        
        # 初始化主窗口和UI控件引用
        self.main_window = None
        self.tab_control = None
        self.delete_valve_button = None
    
    def show_calculator(self, root):
        """
        显示自力式阀计算窗口
        
        Args:
            root: tkinter根窗口
        """
        # 如果窗口已经打开，则聚焦到该窗口
        if self.main_window and self.main_window.winfo_exists():
            self.main_window.focus_set()
            return
        
        # 创建顶层窗口
        self.main_window = tk.Toplevel(root)
        self.main_window.title("自力式阀计算")
        self.main_window.geometry("600x600")
        
        # 设置窗口图标
        if hasattr(self.parent, 'icon_path') and os.path.exists(self.parent.icon_path):
            self.main_window.iconbitmap(self.parent.icon_path)
        
        # 创建主框架
        main_frame = ttk.Frame(self.main_window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建选项卡控件
        self.tab_control = ttk.Notebook(main_frame)
        self.tab_control.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建自力式阀1标签页
        self._create_first_tab()
        
        # 添加按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        # 创建删除按钮
        self.delete_valve_button = ttk.Button(
            button_frame, 
            text="删除当前标签页", 
            command=self.delete_current_tab,
            state="disabled"
        )
        self.delete_valve_button.pack(side="left", padx=5)
        
        # 添加"新增"按钮
        ttk.Button(
            button_frame, 
            text="新增自力式阀计算", 
            command=self.add_new_tab
        ).pack(side="left", padx=5)
        
        # 添加"关闭"按钮
        ttk.Button(
            button_frame, 
            text="关闭", 
            command=self.close_window
        ).pack(side="right", padx=5)
        
        # 绑定窗口关闭事件
        self.main_window.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
        # 窗口居中显示
        self.main_window.withdraw()
        self.center_window(self.main_window)
        self.main_window.deiconify()
        
        # 加载已保存的自力式阀数据
        self.load_data()
    
    def _create_first_tab(self):
        """创建第一个自力式阀标签页"""
        # 增加计数
        self.valve_tab_count = 1
        
        # 初始化第一个阀门的数据变量
        self.valves_data[1] = {
            "description": tk.StringVar(value=""),
            "max_flow": tk.StringVar(value=""),
            "medium": tk.StringVar(value=""),
            "density": tk.StringVar(value="0.743"),
            "temperature": tk.StringVar(value="40"),
            "pre_pressure": tk.StringVar(value=""),
            "post_pressure": tk.StringVar(value=""),
            "c_calculated": tk.StringVar(value="0"),
            "c_selected": tk.StringVar(value=""),
            "k_percent": tk.StringVar(value="0"),
            "selected_dn": tk.StringVar(value="DN20")
        }
        
        # 创建标签页
        valve1_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(valve1_tab, text="自力式阀1")
        
        # 创建自力式阀1界面
        self.create_valve_tab(valve1_tab, 1)

        # 首次创建时更新设备表
        self.update_equipment_table()
    
    def add_new_tab(self):
        """添加新的自力式阀标签页"""
        # 增加计数
        self.valve_tab_count += 1
        tab_index = self.valve_tab_count
        
        # 初始化新阀门的数据变量
        self.valves_data[tab_index] = {
            "description": tk.StringVar(value=""),
            "max_flow": tk.StringVar(value=""),
            "medium": tk.StringVar(value=""),
            "density": tk.StringVar(value="0.743"),
            "temperature": tk.StringVar(value="40"),
            "pre_pressure": tk.StringVar(value=""),
            "post_pressure": tk.StringVar(value=""),
            "c_calculated": tk.StringVar(value="0"),
            "c_selected": tk.StringVar(value=""),
            "k_percent": tk.StringVar(value="0"),
            "selected_dn": tk.StringVar(value="DN20")
        }
        
        # 创建新标签页
        new_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(new_tab, text=f"自力式阀{tab_index}")
        
        # 创建标签页的界面
        self.create_valve_tab(new_tab, tab_index)
        
        # 选中新添加的标签页
        self.tab_control.select(tab_index - 1)
        
        # 启用删除按钮
        self.delete_valve_button.config(state="normal")

        # 更新设备表
        self.update_equipment_table()
    
    def delete_current_tab(self):
        """删除当前选中的标签页"""
        # 如果只有一个标签页，则不删除
        if self.valve_tab_count <= 1:
            return
        
        # 获取当前选中标签页的索引
        current_tab_index = self.tab_control.index("current")
        
        # 获取当前标签页的文本，从中提取标签页编号
        tab_text = self.tab_control.tab(current_tab_index, "text")
        tab_number = int(tab_text.replace("自力式阀", ""))
        
        # 删除当前标签页
        self.tab_control.forget(current_tab_index)
        
        # 从数据字典中删除对应的数据
        if tab_number in self.valves_data:
            del self.valves_data[tab_number]
        
        # 更新标签页计数
        self.valve_tab_count -= 1
        
        # 如果只剩一个标签页，则禁用删除按钮
        if self.valve_tab_count <= 1:
            self.delete_valve_button.config(state="disabled")
        
        # 保存更新后的数据
        self.save_data()

        # 更新设备表
        self.update_equipment_table()
    
    def create_valve_tab(self, parent_frame, tab_index):
        """
        创建自力式阀计算标签页
        
        Args:
            parent_frame: 父框架
            tab_index: 标签页索引
        """
        # 获取对应的变量
        valve_data = self.valves_data[tab_index]
        
        # 创建输入框架
        input_frame = ttk.LabelFrame(parent_frame, text="输入数据区")
        input_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建输入控件
        row = 0
        
        # 自力式阀描述
        ttk.Label(input_frame, text=f"自力式阀{tab_index}描述:").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        ttk.Entry(input_frame, textvariable=valve_data["description"], width=40).grid(
            row=row, column=1, sticky="w", padx=5, pady=5
        )
        row += 1
        
        # 最大流量
        ttk.Label(input_frame, text="最大流量(Nm³/h):").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        ttk.Entry(input_frame, textvariable=valve_data["max_flow"], width=15).grid(
            row=row, column=1, sticky="w", padx=5, pady=5
        )
        row += 1
        
        # 介质
        ttk.Label(input_frame, text="介质:").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        ttk.Entry(input_frame, textvariable=valve_data["medium"], width=15).grid(
            row=row, column=1, sticky="w", padx=5, pady=5
        )
        row += 1
        
        # 介质密度
        ttk.Label(input_frame, text="介质密度(kg/m³):").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        ttk.Entry(input_frame, textvariable=valve_data["density"], width=15).grid(
            row=row, column=1, sticky="w", padx=5, pady=5
        )
        row += 1
        
        # 设计温度
        ttk.Label(input_frame, text="设计温度(℃):").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        ttk.Entry(input_frame, textvariable=valve_data["temperature"], width=15).grid(
            row=row, column=1, sticky="w", padx=5, pady=5
        )
        row += 1
        
        # 阀前压力
        ttk.Label(input_frame, text="阀前压力(MPa):").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        ttk.Entry(input_frame, textvariable=valve_data["pre_pressure"], width=15).grid(
            row=row, column=1, sticky="w", padx=5, pady=5
        )
        row += 1
        
        # 阀后压力
        ttk.Label(input_frame, text="阀后压力(MPa):").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        ttk.Entry(input_frame, textvariable=valve_data["post_pressure"], width=15).grid(
            row=row, column=1, sticky="w", padx=5, pady=5
        )
        row += 1
        
        # 创建计算结果框架
        result_frame = ttk.LabelFrame(parent_frame, text="计算结果区")
        result_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建计算结果控件
        row = 0
        
        # C计算
        ttk.Label(result_frame, text="C计算:").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        ttk.Label(result_frame, textvariable=valve_data["c_calculated"]).grid(
            row=row, column=1, sticky="w", padx=5, pady=5
        )
        row += 1
        
        # C选定
        ttk.Label(result_frame, text="C选定:").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        ttk.Entry(result_frame, textvariable=valve_data["c_selected"], width=15).grid(
            row=row, column=1, sticky="w", padx=5, pady=5
        )
        row += 1
        
        # 开度K(%)
        ttk.Label(result_frame, text="开度K(%):").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        ttk.Label(result_frame, textvariable=valve_data["k_percent"]).grid(
            row=row, column=1, sticky="w", padx=5, pady=5
        )
        row += 1

        # 选取DN
        ttk.Label(result_frame, text="选取DN:").grid(
            row=row, column=0, sticky="w", padx=5, pady=5
        )
        dn_values = ["DN15", "DN20", "DN25", "DN32", "DN40", "DN50", "DN65", "DN80", "DN100", "DN125", "DN150", "DN200"]
        dn_combobox = ttk.Combobox(result_frame, textvariable=valve_data["selected_dn"], values=dn_values, width=12, state="readonly")
        dn_combobox.grid(row=row, column=1, sticky="w", padx=5, pady=5)
        row += 1
        
        # 添加计算按钮
        button_frame = ttk.Frame(parent_frame)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        ttk.Button(
            button_frame, 
            text="计算", 
            command=lambda: self.calculate_valve(tab_index)
        ).pack(side="left", padx=5)
        
        # 绑定输入字段变化事件，当任何输入字段改变时自动计算
        valve_data["max_flow"].trace_add("write", lambda *args: self.calculate_valve(tab_index))
        valve_data["density"].trace_add("write", lambda *args: self.calculate_valve(tab_index))
        valve_data["temperature"].trace_add("write", lambda *args: self.calculate_valve(tab_index))
        valve_data["pre_pressure"].trace_add("write", lambda *args: self.calculate_valve(tab_index))
        valve_data["post_pressure"].trace_add("write", lambda *args: self.calculate_valve(tab_index))
        valve_data["c_selected"].trace_add("write", lambda *args: self.calculate_valve_k(tab_index))

        # 绑定设备表更新事件
        valve_data["description"].trace_add("write", lambda *args: self.update_equipment_table())
        valve_data["max_flow"].trace_add("write", lambda *args: self.update_equipment_table())
        valve_data["pre_pressure"].trace_add("write", lambda *args: self.update_equipment_table())
        valve_data["post_pressure"].trace_add("write", lambda *args: self.update_equipment_table())
        valve_data["medium"].trace_add("write", lambda *args: self.update_equipment_table())
        valve_data["c_selected"].trace_add("write", lambda *args: self.update_equipment_table())
        valve_data["selected_dn"].trace_add("write", lambda *args: self.update_equipment_table())
    
    def calculate_valve(self, tab_index):
        """
        计算自力式阀相关参数
        
        Args:
            tab_index: 标签页索引
        """
        try:
            # 获取对应的变量
            valve_data = self.valves_data[tab_index]
            
            # 获取输入数据
            max_flow = float(valve_data["max_flow"].get() or 0)
            density = float(valve_data["density"].get() or 0.743)
            temperature = float(valve_data["temperature"].get() or 40)
            pre_pressure = float(valve_data["pre_pressure"].get() or 0)
            post_pressure = float(valve_data["post_pressure"].get() or 0)
            
            # 检查输入是否有效
            if max_flow <= 0 or density <= 0 or pre_pressure <= 0 or post_pressure <= 0:
                return
            
            # 计算C值
            # 根据要求：如果(阀前压力*10-阀后压力*10<0.52*(阀前压力*10+1)，使用公式1，否则使用公式2
            pre_pressure_bar = pre_pressure * 10
            post_pressure_bar = post_pressure * 10
            delta_p = pre_pressure_bar - post_pressure_bar
            
            if delta_p < 0.52 * (pre_pressure_bar + 1):
                # 公式1: 1.167*最大流量*sqrt(介质密度*(273+设计温度))/(514*(1-0.46*(阀前压力*10-阀后压力*10)/(阀前压力*10+1))*sqrt((阀前压力*10-阀后压力*10)*(阀前压力*10+1))
                c_calculated = (
                    1.167 * max_flow * 
                    math.sqrt(density * (273 + temperature)) / 
                    (514 * (1 - 0.46 * delta_p / (pre_pressure_bar + 1)) * 
                     math.sqrt(delta_p * (pre_pressure_bar + 1)))
                )
            else:
                # 公式2: 1.167*最大流量*sqrt(介质密度*(273+设计温度))/(280*(阀前压力*10+1))
                c_calculated = (
                    1.167 * max_flow * 
                    math.sqrt(density * (273 + temperature)) / 
                    (280 * (pre_pressure_bar + 1))
                )
            
            # 设置计算结果
            valve_data["c_calculated"].set(f"{c_calculated:.2f}")
            
            # 计算开度K
            self.calculate_valve_k(tab_index)
            
        except Exception as e:
            print(f"计算自力式阀{tab_index}参数时出错: {str(e)}")
    
    def calculate_valve_k(self, tab_index):
        """
        计算自力式阀开度K值
        
        Args:
            tab_index: 标签页索引
        """
        try:
            # 获取对应的变量
            valve_data = self.valves_data[tab_index]
            
            # 获取C计算和C选定值
            c_calculated = float(valve_data["c_calculated"].get() or 0)
            c_selected = float(valve_data["c_selected"].get() or 0)
            
            # 检查输入是否有效
            if c_calculated <= 0 or c_selected <= 0:
                valve_data["k_percent"].set("0")
                return
            
            # 计算开度K(%)
            # K(%) = (1 + 0.68*log10(C计算/C选定)) * 100
            k_percent = (1 + 0.68*math.log10(c_calculated / c_selected)) * 100
            
            # 设置计算结果
            valve_data["k_percent"].set(f"{k_percent:.2f}")
            
        except Exception as e:
            print(f"计算自力式阀{tab_index}开度K值时出错: {str(e)}")
            valve_data["k_percent"].set("0")

    def update_equipment_table(self):
        """更新设备表中的自力式调压阀信息"""
        try:
            # 检查是否有父对象和设备管理器
            if hasattr(self.parent, 'equipment_manager') and self.parent.equipment_manager:
                # 调用设备管理器的更新方法
                self.parent.equipment_manager.update_self_operated_pressure_valves()
        except Exception as e:
            print(f"更新设备表时出错: {str(e)}")
    
    def save_data(self):
        """保存自力式阀数据到项目"""
        try:
            # 检查是否有parent对象
            if not self.parent:
                print("没有传入parent对象，无法保存数据")
                return
                
            # 获取当前项目名称和工程代号
            project_name = self.parent.project_name.get()
            project_code = self.parent.project_code.get()
            
            # 如果项目名称或工程代号为空，则不保存
            if not project_name and not project_code:
                print("项目名称和工程代号均为空，无法保存自力式阀数据")
                return
            
            # 读取历史数据
            history = []
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            # 查找当前项目记录
            for record in history:
                if record.get("工程名称") == project_name and record.get("工程代号") == project_code:
                    # 先清理所有的自力式阀相关数据
                    # 收集所有需要清理的键
                    keys_to_remove = []
                    for key in record.keys():
                        if key.startswith("自力式阀") and (
                            key.endswith("描述") or
                            key.endswith("最大流量(Nm³/h)") or
                            key.endswith("介质") or
                            key.endswith("介质密度(kg/m³)") or
                            key.endswith("设计温度(℃)") or
                            key.endswith("阀前压力(MPa)") or
                            key.endswith("阀后压力(MPa)") or
                            key.endswith("C计算") or
                            key.endswith("C选定") or
                            key.endswith("开度K(%)") or
                            key.endswith("数据")
                        ):
                            keys_to_remove.append(key)
                    
                    # 删除所有自力式阀相关数据
                    for key in keys_to_remove:
                        if key in record:
                            del record[key]
                    
                    # 然后保存当前有效的自力式阀数据
                    for idx, valve_data in self.valves_data.items():
                        # 构建阀门数据字典
                        valve_dict = {
                            f"自力式阀{idx}描述": valve_data["description"].get(),
                            "最大流量(Nm³/h)": valve_data["max_flow"].get(),
                            "介质": valve_data["medium"].get(),
                            "介质密度(kg/m³)": valve_data["density"].get(),
                            "设计温度(℃)": valve_data["temperature"].get(),
                            "阀前压力(MPa)": valve_data["pre_pressure"].get(),
                            "阀后压力(MPa)": valve_data["post_pressure"].get(),
                            "C计算": valve_data["c_calculated"].get(),
                            "C选定": valve_data["c_selected"].get(),
                            "开度K(%)": valve_data["k_percent"].get(),
                            "选取DN": valve_data["selected_dn"].get()
                        }
                        
                        # 保存到记录中
                        record[f"自力式阀{idx}数据"] = valve_dict
                        
                        # 同时在顶层保存关键字段，以保持与原代码的兼容性
                        record[f"自力式阀{idx}描述"] = valve_data["description"].get()
                        record[f"自力式阀{idx}最大流量(Nm³/h)"] = valve_data["max_flow"].get()
                        record[f"自力式阀{idx}介质"] = valve_data["medium"].get()
                        record[f"自力式阀{idx}介质密度(kg/m³)"] = valve_data["density"].get()
                        record[f"自力式阀{idx}设计温度(℃)"] = valve_data["temperature"].get()
                        record[f"自力式阀{idx}阀前压力(MPa)"] = valve_data["pre_pressure"].get()
                        record[f"自力式阀{idx}阀后压力(MPa)"] = valve_data["post_pressure"].get()
                        record[f"自力式阀{idx}C计算"] = valve_data["c_calculated"].get()
                        record[f"自力式阀{idx}C选定"] = valve_data["c_selected"].get()
                        record[f"自力式阀{idx}开度K(%)"] = valve_data["k_percent"].get()
                        record[f"自力式阀{idx}选取DN"] = valve_data["selected_dn"].get()
                    
                    # 更新父级对象的valve_tab_count
                    if hasattr(self.parent, 'valve_tab_count'):
                        self.parent.valve_tab_count = self.valve_tab_count
                    
                    # 保存更新后的历史记录
                    with open(self.history_file, 'w', encoding='utf-8') as f:
                        json.dump(history, f, ensure_ascii=False, indent=2)
                    
                    print("自力式阀数据已保存")
                    return
            
            # 如果没有找到当前项目记录，则创建新记录
            print("未找到当前项目记录，无法保存自力式阀数据")
            
        except Exception as e:
            print(f"保存自力式阀数据时出错: {str(e)}")
    def load_data(self):
        """加载已保存的自力式阀数据"""
        try:
            # 检查是否有parent对象
            if not self.parent:
                print("没有传入parent对象，无法加载数据")
                return
                
            # 获取当前项目名称和工程代号
            project_name = self.parent.project_name.get()
            project_code = self.parent.project_code.get()
            
            # 如果历史记录文件存在，读取历史数据
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                    
                # 查找当前项目记录
                for record in history:
                    if record.get("工程名称") == project_name and record.get("工程代号") == project_code:
                        # 收集所有自力式阀数据
                        valve_indices = set()
                        for key in record.keys():
                            if key.startswith("自力式阀") and "描述" in key:
                                valve_index = key.replace("自力式阀", "").replace("描述", "")
                                try:
                                    valve_indices.add(int(valve_index))
                                except:
                                    pass
                        
                        # 如果没有找到任何数据，使用默认值
                        if not valve_indices:
                            return
                        
                        # 根据找到的阀门数量创建标签页
                        max_index = max(valve_indices)
                        
                        # 加载自力式阀1数据
                        self._load_valve_data(record, 1)
                        
                        # 加载其他阀门数据
                        for i in range(2, max_index + 1):
                            if i in valve_indices:
                                # 创建新标签页
                                self.add_new_tab()
                                
                                # 加载数据
                                self._load_valve_data(record, i)
                        
                        return
            
        except Exception as e:
            print(f"加载自力式阀数据时出错: {str(e)}")

    def load_data_from_project(self, project_data):
        """
        从项目数据中加载自力式阀数据

        Args:
            project_data: 项目数据字典
        """
        try:
            # 收集所有自力式阀数据
            valve_indices = set()
            for key in project_data.keys():
                if key.startswith("自力式阀") and "描述" in key:
                    valve_index = key.replace("自力式阀", "").replace("描述", "")
                    try:
                        valve_indices.add(int(valve_index))
                    except:
                        pass

            # 如果没有找到任何数据，返回
            if not valve_indices:
                print("项目数据中没有找到自力式阀数据")
                return

            # 清空现有数据
            self.valves_data.clear()

            # 根据找到的阀门数量创建数据
            for valve_index in sorted(valve_indices):
                # 创建阀门数据
                self.valves_data[valve_index] = {
                    "description": tk.StringVar(value=""),
                    "max_flow": tk.StringVar(value=""),
                    "medium": tk.StringVar(value=""),
                    "density": tk.StringVar(value="0.743"),
                    "temperature": tk.StringVar(value="40"),
                    "pre_pressure": tk.StringVar(value=""),
                    "post_pressure": tk.StringVar(value=""),
                    "c_calculated": tk.StringVar(value=""),
                    "c_selected": tk.StringVar(value=""),
                    "k_percent": tk.StringVar(value=""),
                    "selected_dn": tk.StringVar(value="DN20")
                }

                # 加载数据
                self._load_valve_data_from_project(project_data, valve_index)

            print(f"已从项目数据加载{len(valve_indices)}个自力式阀数据")

        except Exception as e:
            print(f"从项目数据加载自力式阀数据时出错: {str(e)}")

    def _load_valve_data_from_project(self, project_data, valve_index):
        """
        从项目数据中加载单个自力式阀数据

        Args:
            project_data: 项目数据字典
            valve_index: 阀门索引
        """
        # 检查阀门数据是否存在
        if valve_index not in self.valves_data:
            return

        valve_data = self.valves_data[valve_index]

        # 从项目数据中加载
        valve_data["description"].set(project_data.get(f"自力式阀{valve_index}描述", ""))
        valve_data["max_flow"].set(project_data.get(f"自力式阀{valve_index}最大流量(Nm³/h)", ""))
        valve_data["medium"].set(project_data.get(f"自力式阀{valve_index}介质", ""))
        valve_data["density"].set(project_data.get(f"自力式阀{valve_index}介质密度(kg/m³)", "0.743"))
        valve_data["temperature"].set(project_data.get(f"自力式阀{valve_index}设计温度(℃)", "40"))
        valve_data["pre_pressure"].set(project_data.get(f"自力式阀{valve_index}阀前压力(MPa)", ""))
        valve_data["post_pressure"].set(project_data.get(f"自力式阀{valve_index}阀后压力(MPa)", ""))
        valve_data["c_calculated"].set(project_data.get(f"自力式阀{valve_index}C计算", "0"))
        valve_data["c_selected"].set(project_data.get(f"自力式阀{valve_index}C选定", ""))
        valve_data["k_percent"].set(project_data.get(f"自力式阀{valve_index}开度K(%)", "0"))
        valve_data["selected_dn"].set(project_data.get(f"自力式阀{valve_index}选取DN", "DN20"))

    def _load_valve_data(self, record, valve_index):
        """
        从历史记录中加载自力式阀数据
        
        Args:
            record: 历史记录数据
            valve_index: 阀门索引
        """
        # 检查阀门数据是否存在
        if valve_index not in self.valves_data:
            return
            
        valve_data = self.valves_data[valve_index]
        
        # 优先从字典中加载
        valve_dict = record.get(f"自力式阀{valve_index}数据", {})
        if valve_dict:
            valve_data["description"].set(valve_dict.get(f"自力式阀{valve_index}描述", ""))
            valve_data["max_flow"].set(valve_dict.get("最大流量(Nm³/h)", ""))
            valve_data["medium"].set(valve_dict.get("介质", ""))
            valve_data["density"].set(valve_dict.get("介质密度(kg/m³)", "0.743"))
            valve_data["temperature"].set(valve_dict.get("设计温度(℃)", "40"))
            valve_data["pre_pressure"].set(valve_dict.get("阀前压力(MPa)", ""))
            valve_data["post_pressure"].set(valve_dict.get("阀后压力(MPa)", ""))
            valve_data["c_calculated"].set(valve_dict.get("C计算", "0"))
            valve_data["c_selected"].set(valve_dict.get("C选定", ""))
            valve_data["k_percent"].set(valve_dict.get("开度K(%)", "0"))
            valve_data["selected_dn"].set(valve_dict.get("选取DN", "DN20"))
        else:
            # 从顶层字段中加载
            valve_data["description"].set(record.get(f"自力式阀{valve_index}描述", ""))
            valve_data["max_flow"].set(record.get(f"自力式阀{valve_index}最大流量(Nm³/h)", ""))
            valve_data["medium"].set(record.get(f"自力式阀{valve_index}介质", ""))
            valve_data["density"].set(record.get(f"自力式阀{valve_index}介质密度(kg/m³)", "0.743"))
            valve_data["temperature"].set(record.get(f"自力式阀{valve_index}设计温度(℃)", "40"))
            valve_data["pre_pressure"].set(record.get(f"自力式阀{valve_index}阀前压力(MPa)", ""))
            valve_data["post_pressure"].set(record.get(f"自力式阀{valve_index}阀后压力(MPa)", ""))
            valve_data["c_calculated"].set(record.get(f"自力式阀{valve_index}C计算", "0"))
            valve_data["c_selected"].set(record.get(f"自力式阀{valve_index}C选定", ""))
            valve_data["k_percent"].set(record.get(f"自力式阀{valve_index}开度K(%)", "0"))
            valve_data["selected_dn"].set(record.get(f"自力式阀{valve_index}选取DN", "DN20"))
    
    def close_window(self):
        """关闭自力式阀计算窗口，并保存数据"""
        try:
            # 保存数据
            self.save_data()
            # 使用显示弹窗的保存项目方法
            if self.parent:
                self.parent.save_project(show_message=True)
            # 关闭窗口
            if self.main_window:
                self.main_window.destroy()
                self.main_window = None
        except Exception as e:
            print(f"关闭自力式阀窗口时出错: {str(e)}")
            if self.main_window:
                self.main_window.destroy()
                self.main_window = None
    
    def on_window_close(self):
        """窗口关闭事件处理函数"""
        try:
            # 保存数据
            self.save_data()
            # 添加：调用保存项目方法确保所有数据被保存到文件
            if self.parent:
                self.parent.save_project(show_message=False)
            # 关闭窗口
            if self.main_window:
                self.main_window.destroy()
                self.main_window = None
        except Exception as e:
            print(f"关闭自力式阀窗口时出错: {str(e)}")
            if self.main_window:
                self.main_window.destroy()
                self.main_window = None
    
    def center_window(self, window):
        """
        使窗口在屏幕中居中显示
        
        Args:
            window: 要居中显示的窗口
        """
        if not window:
            return
        
        window.update_idletasks()
        
        # 获取屏幕宽度和高度
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        # 获取窗口宽度和高度
        window_width = window.winfo_width()
        window_height = window.winfo_height()
        
        # 计算窗口居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        # 设置窗口位置
        window.geometry(f"{window_width}x{window_height}+{x}+{y}") 