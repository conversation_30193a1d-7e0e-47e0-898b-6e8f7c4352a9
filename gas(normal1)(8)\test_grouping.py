#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试小炉阀门分组功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入模块
try:
    from gas_cal import GasCalculator
except ImportError:
    # 尝试直接导入文件
    import importlib.util
    spec = importlib.util.spec_from_file_location("gas_cal", os.path.join(current_dir, "gas(normal1)(8)", "gas_cal.py"))
    if spec is None:
        spec = importlib.util.spec_from_file_location("gas_cal", os.path.join(current_dir, "gas_cal.py"))
    gas_cal = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(gas_cal)
    GasCalculator = gas_cal.GasCalculator

def test_grouping_functionality():
    """测试分组功能"""
    print("开始测试小炉阀门分组功能...")
    
    # 创建主窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建计算器实例
    calculator = GasCalculator(root)
    
    # 设置测试数据
    calculator.furnace_count.set("6")  # 设置6个小炉
    calculator.update_furnace_display()  # 更新小炉显示
    
    # 设置小炉数据 - 创建3组不同的平均热负荷和浮动值
    test_data = [
        {"heat_load": "100", "float_value": "10"},  # 小炉1 - 组1
        {"heat_load": "100", "float_value": "10"},  # 小炉2 - 组1
        {"heat_load": "90", "float_value": "15"},   # 小炉3 - 组2
        {"heat_load": "90", "float_value": "15"},   # 小炉4 - 组2
        {"heat_load": "110", "float_value": "5"},   # 小炉5 - 组3
        {"heat_load": "110", "float_value": "5"},   # 小炉6 - 组3
    ]
    
    # 填充测试数据
    for i, data in enumerate(test_data):
        if i < len(calculator.furnace_data):
            calculator.furnace_data[i]['heat_load'].set(data['heat_load'])
            calculator.furnace_data[i]['float_value'].set(data['float_value'])
    
    print("已设置测试数据:")
    for i, data in enumerate(test_data):
        print(f"  小炉{i+1}: 平均热负荷={data['heat_load']}%, 浮动值={data['float_value']}%")
    
    # 打开调节阀计算窗口
    calculator.show_valve_calculator()
    
    # 测试分组功能
    if hasattr(calculator.valve_calculator, 'group_furnaces_by_heat_load_and_float'):
        groups = calculator.valve_calculator.group_furnaces_by_heat_load_and_float()
        print(f"\n分组结果:")
        for group_key, furnace_indices in groups.items():
            heat_load, float_value = group_key.split('_')
            furnace_numbers = [str(i+1) for i in furnace_indices]
            print(f"  组 {group_key}: 平均热负荷={heat_load}%, 浮动值={float_value}%, 小炉={','.join(furnace_numbers)}")
        
        expected_groups = 3
        actual_groups = len(groups)
        if actual_groups == expected_groups:
            print(f"\n✓ 分组测试通过: 预期{expected_groups}组，实际{actual_groups}组")
        else:
            print(f"\n✗ 分组测试失败: 预期{expected_groups}组，实际{actual_groups}组")
    else:
        print("\n✗ 分组方法不存在")
    
    print("\n测试完成。请在调节阀计算窗口中查看分组显示效果。")
    print("按Ctrl+C退出测试。")
    
    # 运行主循环
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        root.quit()

if __name__ == "__main__":
    test_grouping_functionality()
